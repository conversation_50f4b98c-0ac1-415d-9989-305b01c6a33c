using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.Common.Utils;
using GolfFantaCore.DTOs;
using GolfFantaCore.Entities.Database;
using GolfFantaCore.Models.Bag;
using GolfFantaCore.Models.Challenge;
using GolfFantaCore.Models.Common;
using GolfFantaCore.Models.PlayerData;
using GolfFantaCore.Models.Tour;
using GolfFantaCore.Models.Tournament;
using GolfFantaModule.Common;
using GolfFantaModule.Extensions;
using GolfFantaModule.SDKs;
using GolfFantaModule.Supabase.Entities;
using GolfFantaModule.Supabase.Entities.Tour;
using GolfFantaModule.Supabase.Services.Interfaces;
using Newtonsoft.Json;
using Supabase.Interfaces;
using Supabase.Postgrest.Models;
using Supabase.Postgrest.Responses;
using Unity.Services.CloudCode.Apis;
using Unity.Services.CloudCode.Core;
using Unity.Services.Economy.Model;
using Operator = Supabase.Postgrest.Constants.Operator;
using Utils = GolfFantaModule.Common.Utils;

namespace GolfFantaModule.Supabase.Services.Implements
{
    public class InternalDatabaseService : IInternalDatabaseService
    {
        public InternalDatabaseService()
        {

        }
        
        private string _userId;
        private readonly InternalDatabase _database = new InternalDatabase();
        
        public async Task Initialize(IExecutionContext context, IGameApiClient gameApiClient, bool isCache)
        {
            _userId = await _database.Initialize(context, gameApiClient, isCache);
        }
        
        public string GetUserId() => _userId;

        public ISupabaseTable<TModel> From<TModel>() where TModel : BaseModel, new() => _database.From<TModel>();

        #region Common
        
        public async Task<string> GetPlayerSaves(IExecutionContext context, List<string> keysToFetch)
        {
            const string tournamentParticipation = "TournamentParticipation";
            const string playerInfo = "PlayerInfo";
            const string playerGadget = "PlayerGadget";
            var tasks = new Dictionary<string, Task<object>>();
            var data = new Dictionary<string, object>();

            foreach (var key in keysToFetch)
            {
                switch (key)
                {
                    case tournamentParticipation:
                        tasks.Add(tournamentParticipation, GetParticipationsWithRelatedAttempts(context).ContinueWith<object>(t => t.Result.Select(participation => DataConversionExtensions.ToTournamentParticipationModel(participation))));
                        break;
                    case playerInfo:
                        tasks.Add(playerInfo, GetPlayerInfo(context).ContinueWith<object>(t => t.Result));
                        break;
                    case playerGadget:
                        tasks.Add(playerGadget, GetPlayerGadgetSaveData(context).ContinueWith<object>(t => t.Result));
                        break;
                }
            }

            await Task.WhenAll(tasks.Values);
            var result = tasks.ToDictionary(
                pair => pair.Key, 
                pair => pair.Value.Result
            );
            return JsonConvert.SerializeObject(result, Formatting.Indented);
        }
        
        public async Task<bool> GetDailyBagReceivedStatus(IExecutionContext context)
        {
            try
            {
                var database = _database;
                var dailyReward = await database
                    .From<DailyRewardEntity>()
                    .Filter("user_id", Operator.Equals, _userId)
                    .Single();
                
                return dailyReward?.LastReceivedDailyBag.Date == DateTime.Today;
            }
            catch (Exception ex) 
            {
                throw new Exception($"Failed to GetDailyBagReceivedStatus for playerId {context.PlayerId}. Error: {ex.Message}");
            }
        }
        
        public async Task<bool> GetNPassStatus(IExecutionContext context)
        {
            try
            {
                var database = _database;
                var dailyReward = await database
                    .From<DailyRewardEntity>()
                    .Filter("user_id", Operator.Equals, _userId)
                    .Single();
                return dailyReward?.LastNPass.Date == DateTime.Today;
            }
            catch (Exception ex) 
            {
                throw new Exception($"Failed to GetDailyBagReceivedStatus for playerId {context.PlayerId}. Error: {ex.Message}");
            }
        }
        
        public async Task<bool> GetRPassStatus(IExecutionContext context)
        {
            try
            {
                var database = _database;
                var dailyReward = await database
                    .From<DailyRewardEntity>()
                    .Filter("user_id", Operator.Equals, _userId)
                    .Single();
                return dailyReward?.LastRPass.Date == DateTime.Today;
            }
            catch (Exception ex) 
            {
                throw new Exception($"Failed to GetDailyBagReceivedStatus for playerId {context.PlayerId}. Error: {ex.Message}");
            }
        }
        
        public async Task<PlayerInfoModel> GetPlayerInfo(IExecutionContext context, string? playerId = null)
        {
            var currentPlayerId = playerId ?? context.PlayerId;
            if (string.IsNullOrEmpty(currentPlayerId))
                throw new Exception("Null player Id");
            
            var player = await _database.From<PlayerPublic>().Filter("unity_player_id", Operator.Equals, currentPlayerId).Single();
            if(player == null)
                throw new Exception($"No player found. UserId: {_userId}. PlayerId: {context.PlayerId}");
            return new PlayerInfoModel(DataConversionExtensions.ToPlayerInfoModel(player));
        }
        
        public async Task<Dictionary<string, PlayerInfoModel>> GetPlayerInfoInBatch(IExecutionContext context, params string[] playerIds)
        {
            var fetchPlayerResponse = await _database.From<PlayerPublic>().Filter("unity_player_id", Operator.In, playerIds).Get();
            var players = fetchPlayerResponse.Models.ToDictionary(
                player => player.UnityPlayerId,
                player => new PlayerInfoModel(DataConversionExtensions.ToPlayerInfoModel(player)));
            
            return playerIds.ToDictionary(playerId => playerId, playerId => players.TryGetValue(playerId, out var player) ? player : new PlayerInfoModel(DefaultData.DefaultPlayerInfo));
        }

        public async Task<List<TourModel>> GetTourConfigs(GameMode gameMode)
        {
            var tourEntities = await _database.From<TourEntity>()
                .Filter("game_mode", Operator.Equals, gameMode.ToString())
                .Filter("status", Operator.Equals, EntityStatus.Active.ToString())
                .Get();

            var tourModels = tourEntities.Models.Select(tourEntity => tourEntity.ToTourModel()).ToList();
            
            return tourModels;
        }

        public async Task<Dictionary<AssetBundleKey, string>> GetBundleVersions(IExecutionContext context, Platform platform)
        {
            var result = new Dictionary<AssetBundleKey, string>();

            foreach (AssetBundleKey key in Enum.GetValues(typeof(AssetBundleKey)))
            {
                var fetched = await _database.From<BundleVersionEntity>()
                    .Filter("bundle_id", Operator.Equals, EnumUtils.GetDisplayName(key))
                    .Single();

                switch (platform)
                {
                    case Platform.Android:
                        if (!string.IsNullOrEmpty(fetched?.AddressableVersion.android))
                        {
                            result[key] = fetched?.AddressableVersion.android!;
                        }
                        break;
                    case Platform.IOS:
                        if (!string.IsNullOrEmpty(fetched?.AddressableVersion.ios))
                        {
                            result[key] = fetched?.AddressableVersion.ios!;
                        }
                        break;
                }
            }

            return result;
        }
        
        public async Task<Dictionary<AssetBundleKey, string>> GetBundleUrls(IExecutionContext context, Platform platform, List<AssetBundleKey> keys)
        {
            var result = new Dictionary<AssetBundleKey, string>();
            
            foreach (var key in keys)
            {
                var fetched = await _database.From<BundleVersionEntity>()
                    .Filter("bundle_id", Operator.Equals, EnumUtils.GetDisplayName(key))
                    .Single();
                
                switch (platform)
                {
                    case Platform.Android:
                        var androidPath =
                            $"{EnumUtils.GetDisplayName(platform).ToLower()}/{key}_{fetched?.AddressableVersion.android}.zip";
                        result[key] = await _database.GetSignDownloadUrl(Constants.BundleBucket, androidPath);
                        break;
                    case Platform.IOS:
                        var iosPath =
                            $"{EnumUtils.GetDisplayName(platform).ToLower()}/{key}_{fetched?.AddressableVersion.ios}.zip";
                        result[key] = await _database.GetSignDownloadUrl(Constants.BundleBucket, iosPath);
                        break;
                }
            }

            return result;
        }
        
        public async Task<List<TournamentParticipationEntity>> GetParticipationsWithRelatedAttempts(IExecutionContext context, params string[] ids)
        {
            var query = _database.From<TournamentParticipationEntity>()
                .Select("*, tournament_attempt(*)")
                .Filter("user_id", Operator.Equals, GetUserId());

            // Apply filtering if IDs are provided
            if (ids?.Length > 0)
                query = query.Filter("tournament_uuid", Operator.In, ids);

            var result = await query.Get();
            return result.Models;
        }

        public async Task<PlayerGadgetSaveData> GetPlayerGadgetSaveData(IExecutionContext context)
        {
            var playerGadget =  await _database.From<PlayerGadgetEntity>()
                .Filter("user_id", Operator.Equals, _userId)
                .Single();
            return playerGadget == null ? new PlayerGadgetSaveData() : DataConversionExtensions.ToPlayerGadgetSaveData(playerGadget);
        }
        
        #endregion

        
        #region Other

        public async Task<global::Supabase.Gotrue.Session> SignUp(IExecutionContext context, IGameApiClient gameApiClient, string playerId)
        {
            return await _database.SignUp(context, gameApiClient, playerId);
        }
        
        #endregion

        #region Migrate

        /// <summary>
        /// Contain available versions of database.
        /// DO NOT ADD ANYTHING THAN DATABASE VERSION
        /// </summary>
        public static class DatabaseVersions
        {
            public const int DailyBag = 1;
            public const int DailyNPass = 2;
            public const int VirtualPurchase = 3;
            public const int PlayerRewardBags = 4;
            public const int PlayerGadgets = 5;
            public const int PlayerStats = 6;
            public const int TournamentParticipation = 7;
            
            private static readonly int _latestVersion = typeof(DatabaseVersions)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.DeclaredOnly)
                .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(int)) // Only constant integers
                .Select(f => (int)f.GetRawConstantValue()!)
                .Max();
            public static int LatestVersion => _latestVersion;
        }

        public async Task<string> GetHoleDownloadUrl(IExecutionContext context, IGameApiClient gameApiClient, HoleRequestData requestData)
        {
            var version = await _database.GetHoleVersion(requestData.Platform, requestData.Guid);
            return await _database.GetSignDownloadUrl(requestData.BucketName, requestData.GetPath(version));
        }
        
        public async Task<string> GetHoleVersion(IExecutionContext context, IGameApiClient gameApiClient, HoleVersionRequestData requestData)
        {
            return await _database.GetHoleVersion(requestData.Platform, requestData.Guid);
        }

        public async Task CheckVersionAndMigrate(IExecutionContext context, IGameApiClient gameApiClient)
        {
            var databaseVersion = await _database.From<DatabaseVersionEntity>()
                .Filter("user_id", Operator.Equals, _userId)
                .Single();
            var createNewInstance = databaseVersion == null;
            databaseVersion ??= new DatabaseVersionEntity(_userId);

            var migrations = new Dictionary<int, Func<string, IExecutionContext, IGameApiClient, Task>>
            {
                { DatabaseVersions.DailyBag, MigrateToVersion1 },
                { DatabaseVersions.DailyNPass, MigrateToVersion2 },
                { DatabaseVersions.VirtualPurchase, MigrateToVersion3 },
                { DatabaseVersions.PlayerRewardBags, MigrateToVersion4 },
                { DatabaseVersions.PlayerGadgets, MigrateToVersion5 },
                { DatabaseVersions.PlayerStats, MigrateToVersion6 },
                { DatabaseVersions.TournamentParticipation, MigrateToVersion7 },
            };
            
            var versionsToMigrate = migrations.Keys.Where(version => databaseVersion.Version < version);
            foreach (var version in versionsToMigrate)
            {
                await migrations[version](_userId, context, gameApiClient);
                databaseVersion.Version = version;
                Func<Task<ModeledResponse<DatabaseVersionEntity>>> query = createNewInstance 
                    ? () => _database.From<DatabaseVersionEntity>().Insert(databaseVersion) 
                    : () => _database.From<DatabaseVersionEntity>().Update(databaseVersion);
                await Utils.ExecuteWithRetry(query);
            }
        }

        private async Task MigrateToVersion1(string userId,  IExecutionContext context, IGameApiClient gameApiClient)
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.DailyBagReceivedStatusKey });
                
            var lastReceivedDailyBag = (DateTime?) getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value;
            if (lastReceivedDailyBag == null) return;
            
            var dailyReward = await _database.From<DailyRewardEntity>()
                .Filter("user_id", Operator.Equals, userId)
                .Single();
            var createNewInstance = dailyReward == null;
            dailyReward ??= new DailyRewardEntity(userId);
            dailyReward.LastReceivedDailyBag = lastReceivedDailyBag.Value;
            
            Func<Task<ModeledResponse<DailyRewardEntity>>> query = createNewInstance 
                ? () => _database.From<DailyRewardEntity>().Insert(dailyReward) 
                : () => _database.From<DailyRewardEntity>().Update(dailyReward);
            
            var result = await Utils.ExecuteWithRetry(query);
            if (result == null) 
                throw new Exception($"Could not write DailyReward for UserId: {userId}. LastReceived: {lastReceivedDailyBag}");
        }
        
        private async Task MigrateToVersion2(string userId, IExecutionContext context, IGameApiClient gameApiClient) //DailyNPass
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.NPassStatusKey });
            
            var lastReceivedNPass = (DateTime?) getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value;
            if (lastReceivedNPass == null) return;
            
            var dailyReward = await _database.From<DailyRewardEntity>()
                .Filter("user_id", Operator.Equals, userId)
                .Single();
            var createNewInstance = dailyReward == null;
            dailyReward ??= new DailyRewardEntity(userId);
            dailyReward.LastNPass = lastReceivedNPass.Value;

            Func<Task<ModeledResponse<DailyRewardEntity>>> query = createNewInstance 
                ? () => _database.From<DailyRewardEntity>().Insert(dailyReward) 
                : () => _database.From<DailyRewardEntity>().Update(dailyReward);
            
            var result = await Utils.ExecuteWithRetry(query);
            if (result == null) 
                throw new Exception($"Could not write DailyReward for UserId: {userId}. LastReceived: {lastReceivedNPass}");
        }
        
        private async Task MigrateToVersion3(string userId, IExecutionContext context, IGameApiClient gameApiClient) //VirtualPurchase
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.IAPOfferPurchasesKey });

            var cloudSaveDataJson = getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString();
            if(string.IsNullOrEmpty(cloudSaveDataJson)) return;
            
            var packageIds = JsonConvert.DeserializeObject<List<string>>(getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString()!);
            
            var economyConfigs = await gameApiClient.EconomyConfiguration.GetPlayerConfigurationAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!);
            var virtualPurchaseConfigs =
                economyConfigs.Data.Results.Where(config => config.GetType() == typeof(VirtualPurchaseResource))
                    .Select(config => config.GetVirtualPurchaseResource()).ToList();
            var tasks = new List<Task<List<VirtualPurchaseEntity>?>>();
            
            foreach (var packageId in packageIds)
            {
                var matchingConfig = virtualPurchaseConfigs.FirstOrDefault(config => config.Id == packageId);
                if (matchingConfig == null) continue;
                
                var virtualPurchase = new VirtualPurchaseEntity(userId);
                virtualPurchase.ItemId = packageId;

                tasks.Add(Utils.ExecuteWithRetry(()=>_database.From<VirtualPurchaseEntity>().Insert(virtualPurchase)));
            }
            await Task.WhenAll(tasks);
            
            if (tasks.Any(task => task.Result == null)) 
                throw new Exception($"Could not write VirtualPurchase for UserId: {userId}.");
        }
        
        private async Task MigrateToVersion4(string userId, IExecutionContext context, IGameApiClient gameApiClient) //PlayerBags
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.PlayerBagListData });
            
            var cloudSaveDataJson = getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString();
            if (string.IsNullOrEmpty(cloudSaveDataJson)) return;

            var playerBagsData = JsonConvert.DeserializeObject<PlayerDataBagListModel>(getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString()!);
            
            var tasks = new List<Task<List<PlayerRewardBagEntity>?>>();
            if (playerBagsData?.playerBagList != null)
            {
                foreach (var bagData in playerBagsData.playerBagList)
                {
                    var playerBag = bagData.ToPlayerRewardBag(userId);
                    if(bagData.paidFor) playerBag.SkippedBy = "gems";
                    playerBag.OpenedTime = null;
                    tasks.Add(Utils.ExecuteWithRetry(()=>_database.From<PlayerRewardBagEntity>().Insert(playerBag)));
                }
                await Task.WhenAll(tasks);
            }
                
            if (tasks.Any(task => task.Result == null)) 
                throw new Exception($"Could not write PlayerBag for UserId: {userId}.");
        }
        
        private async Task MigrateToVersion5(string userId, IExecutionContext context, IGameApiClient gameApiClient) //PlayerGadget
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.PlayerGadget });
            
            var cloudSaveDataJson = getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString();
            if (string.IsNullOrEmpty(cloudSaveDataJson)) return;

            var playerGadgetSaveData = JsonConvert.DeserializeObject<PlayerGadgetSaveData>(getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString()!)!;
            
            var playerGadget = await _database.From<PlayerGadgetEntity>()
                .Filter("user_id", Operator.Equals, userId)
                .Single();
            var createNewInstance = playerGadget == null;
            playerGadget ??= playerGadgetSaveData.ToPlayerGadget(userId);

            Func<Task<ModeledResponse<PlayerGadgetEntity>>> query = createNewInstance 
                ? () => _database.From<PlayerGadgetEntity>().Insert(playerGadget) 
                : () => _database.From<PlayerGadgetEntity>().Update(playerGadget);
            
            var result = await Utils.ExecuteWithRetry(query);
            if (result == null)
                throw new Exception($"Could not write PlayerGadget for UserId: {userId}. PlayerGadGet: {JsonConvert.SerializeObject(playerGadget)}");
        }
        
        private async Task MigrateToVersion6(string userId, IExecutionContext context, IGameApiClient gameApiClient) //PlayerStats
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.PlayerInfo });
            
            var cloudSaveDataJson = getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString();
            if (string.IsNullOrEmpty(cloudSaveDataJson)) return;

            var playerInfoModel = JsonConvert.DeserializeObject<PlayerInfoModel>(getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString()!);
            
            var player = (await _database.From<PlayerEntity>()
                .Filter("user_id", Operator.Equals, userId)
                .Single())!;
            
            if(playerInfoModel?.avatarId != null) 
                player.AvatarId = playerInfoModel.avatarId;
            if(playerInfoModel?.rank.rankId != null) 
                player.RankId = playerInfoModel.rank.rankId;
            if(playerInfoModel?.highestH2hRankId != null) 
                player.HighestH2hRankId = playerInfoModel.highestH2hRankId;
            player.Elo = playerInfoModel?.rank.currentPoint ?? 0;
            player.TotalH2hGames = playerInfoModel?.totalH2hGames ?? 0;
            player.TotalH2hGamesWon = playerInfoModel?.totalH2hGamesWon ?? 0;
            player.H2hWinStreak = playerInfoModel?.h2hWinStreak ?? 0;
            player.LongestH2hWinStreak = playerInfoModel?.longestH2hWinStreak ?? 0;
            player.LongestDrive = playerInfoModel?.longestDrive ?? 0;
            player.HolesInOne = playerInfoModel?.holesInOne ?? 0;
            player.Albatrosses = playerInfoModel?.albatrosses ?? 0;
            player.Eagles = playerInfoModel?.eagles ?? 0;
            player.Birdies = playerInfoModel?.birdies ?? 0;
            player.TotalH2hCoinsWon = playerInfoModel?.totalH2hCoinsWon ?? 0;
            player.TotalH2hElo = playerInfoModel?.totalH2hElo ?? 0;
            
            var result = await Utils.ExecuteWithRetry(() => _database.From<PlayerEntity>().Update(player));
            if (result == null)
                throw new Exception($"Could not write PlayerGadget for UserId: {userId}. PlayerGadGet: {JsonConvert.SerializeObject(player)}");
        }
        
        private async Task MigrateToVersion7(string userId, IExecutionContext context, IGameApiClient gameApiClient) //TournamentParticipation
        {
            var getCloudSaveResponse = await gameApiClient.CloudSaveData.GetItemsAsync(context, context.AccessToken,
                context.ProjectId, context.PlayerId!, new List<string> { CloudSaveKey.TournamentParticipation });
            
            var cloudSaveDataJson = getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString();
            if (string.IsNullOrEmpty(cloudSaveDataJson)) return;

            var tournamentParticipationModels = JsonConvert.DeserializeObject<List<TournamentParticipationModel>>(getCloudSaveResponse.Data.Results.FirstOrDefault()?.Value.ToString()!);
            if (tournamentParticipationModels == null || tournamentParticipationModels.Count == 0) return;

            var allTournamentParticipation = new List<TournamentParticipationEntity>();
            var allTournamentAttempts = new List<TournamentAttemptEntity>();

            foreach (var tournamentParticipationModel in tournamentParticipationModels)
            {
                var newUuidV4 = tournamentParticipationModel.tournamentGuid switch
                {
                    "76ZM1ejoHkmn/rZksSCfKA==" => "fb377784-4451-4cf9-901a-c271afb8ca4d",
                    "p9VuCIBqZ0CFwS8pDPSZFA==" => "be96c8d0-251e-4606-8408-95ad8b5eedba",
                    "q9VuCIBqZ0CFwS8pDPSZFA==" => "d229fe21-3cca-4b80-b23b-f2b290cc9fd5",
                    "5Rqqnlvz70yErSsEfPe/QQ==" => "77dc68ab-ebf9-411e-8b47-efd359f59681",
                    "Dev_Tour" => "8314b33b-acf2-4857-bbd6-1ba8e4c240be"
                };
                tournamentParticipationModel.tournamentGuid = newUuidV4;

                var newTournamentParticipation = tournamentParticipationModel.ToTournamentParticipation(userId);
                allTournamentParticipation.Add(newTournamentParticipation);
                allTournamentAttempts.AddRange(newTournamentParticipation.TournamentAttempts);
            }
            
            var insertTournamentParticipationTask = Utils.ExecuteWithRetry(() => _database.From<TournamentParticipationEntity>().Insert(allTournamentParticipation));
            var insertTournamentAttemptsTask = Utils.ExecuteWithRetry(() => _database.From<TournamentAttemptEntity>().Insert(allTournamentAttempts));
            await Task.WhenAll(insertTournamentParticipationTask, insertTournamentAttemptsTask);
        }

        public async Task AddShotDataToActivity(List<GolfShotDataModel> golfShots)
        {
            List<ShotEntity> shotList = new List<ShotEntity>();

            foreach (GolfShotDataModel golfShot in golfShots)
            {
                ShotEntity newShotEntity = new ShotEntity(
                    userId: GetUserId(),
                    activityUuid: golfShot.ActivityUuid,
                    holeUuid: golfShot.HoleUuid,
                    club: golfShot.Club,
                    ball: golfShot.Ball,
                    gear: golfShot.Gear,
                    ballSpeed: golfShot.BallSpeed,
                    launchAngle: golfShot.LaunchAngle,
                    sideAngle: golfShot.SideAngle,
                    backSpin: golfShot.BackSpin,
                    sideSpin: golfShot.SideSpin,
                    carryDistance: golfShot.CarryDistance,
                    rollDistance: golfShot.RollDistance,
                    totalDistance: golfShot.TotalDistance,
                    offlineDistance: golfShot.OfflineDistance,
                    maxHeight: golfShot.MaxHeight,
                    rating: golfShot.ShotRating,
                    weatherCondition: golfShot.WeatherCondition,
                    additionalData: golfShot.AdditionalData
                );
                shotList.Add(newShotEntity);
            }
            await From<ShotEntity>().Insert(shotList);
        }

        #endregion
    }
}