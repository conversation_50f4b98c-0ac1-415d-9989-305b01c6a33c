using GolfFantaModule.Common;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GolfFantaCore.Common;
using GolfFantaCore.DTOs;
using GolfFantaCore.Entities.Database;
using GolfFantaCore.Models;
using GolfFantaCore.Models.Common;
using GolfFantaCore.Models.Economy;
using GolfFantaCore.Models.Economy.PlayerInventory;
using GolfFantaCore.Models.PlayerData;
using GolfFantaCore.Models.Tournament;
using GolfFantaModule.CustomService.Interfaces;
using GolfFantaModule.Extensions;
using GolfFantaModule.Supabase.Services.Interfaces;
using Supabase.Postgrest.Responses;
using Unity.Services.CloudCode.Apis;
using Unity.Services.CloudCode.Core;
using Unity.Services.PlayerNames.Model;
using GolfFantaModule.UnityGameServices.Services.Interfaces;
using Constants = Supabase.Postgrest.Constants;
using Utils = GolfFantaModule.Common.Utils;

namespace GolfFantaModule.Modules
{
    public class CommonModule : APIModule
    {
        private IChallengeService _challengeService;
        
        public CommonModule(
            ILogger<GolfFantaModule> _logger,
            IExecutionContext _context,
            IGameApiClient _gameApiClient,
            IAdminApiClient _adminApiClient,
            ICloudSaveServices cloudSaveServices,
            IRemoteConfigServices remoteConfigServices,
            IEconomyServices economyServices,
            IInternalDatabaseService internalDatabaseService,
            ICustomServices customServices,
            IChallengeService challengeService,
            APIClientRequest apiClientRequest
        ) : base(_logger, _context, _gameApiClient, _adminApiClient, cloudSaveServices, remoteConfigServices, economyServices,internalDatabaseService, customServices, apiClientRequest)
        {
            _challengeService = challengeService;
        }

        public override async Task<APIServerResponse> Process()
        {
            switch (ApiClientRequest.code)
            {
                case APIRequestMethod.GetPlayerRank:
                {
                    return await GetPlayerRank(ApiClientRequest);
                }
                case APIRequestMethod.GetEconomyBallInventoryItems:
                {
                    return await GetEconomyBallInventoryItems(ApiClientRequest);
                }
                case APIRequestMethod.GetEconomyClubInventoryItems:
                {
                    return await GetEconomyClubInventoryItems(ApiClientRequest);
                }
                case APIRequestMethod.GetEconomyGearInventoryItems:
                {
                    return await GetEconomyGearInventoryItems(ApiClientRequest);
                }
                case APIRequestMethod.GetEconomyAvatarInventoryItems:
                {
                    return await GetEconomyAvatarItems(ApiClientRequest);
                }
                case APIRequestMethod.SetPlayerAvatar:
                {
                    return await SetPlayerAvatar(ApiClientRequest);
                }
                case APIRequestMethod.GetEconomyConfigurationRealMoneyPurchases:
                {
                    return await GetEconomyConfigurationRealMoneyPurchases(ApiClientRequest);
                }
                case APIRequestMethod.GetPlayerInventoryItems:
                {
                    return await GetPlayerInventoryItems(ApiClientRequest);
                }
                case APIRequestMethod.UpgradePlayerClubInventoryItem:
                {
                    return await UpgradePlayerClubInventoryItem(ApiClientRequest);
                }
                case APIRequestMethod.RefinePlayerClubInventoryItem:
                {
                    return await RefinePlayerClubInventoryItem(ApiClientRequest);
                }
                case APIRequestMethod.GetCourseConfigByRank:
                {
                    return await GetCourseConfigByRank(ApiClientRequest);
                }
                case APIRequestMethod.GetMultipleConfigs:
                {
                    return await GetMultipleConfigs(ApiClientRequest);
                }
                case APIRequestMethod.GetBucketsInfo:
                {
                    return GetBucketsInfo(ApiClientRequest);
                }
                case APIRequestMethod.GetMultiplePlayerSaves:
                {
                    return await GetMultiplePlayerSaves(ApiClientRequest);
                }
                case APIRequestMethod.ValidateCurrency:
                {
                    return await ValidateCurrency(ApiClientRequest);
                }
                case APIRequestMethod.SetPlayerGadget:
                {
                    return await SetPlayerGadget(ApiClientRequest);
                }
                case APIRequestMethod.GetPlayersInfo:
                {
                    return await GetPlayersInfo(ApiClientRequest);
                }
                case APIRequestMethod.GetPlayerNames:
                {
                    return await GetPlayerNames(ApiClientRequest);
                }
                case APIRequestMethod.CheckDbVersionAndMigrate:
                {
                    return await CheckDbVersionAndMigrate(ApiClientRequest);
                }
                case APIRequestMethod.SetPlayerName:
                {
                    return await SetPlayerName(ApiClientRequest);
                }
                case APIRequestMethod.CheckAndInitializeNewPlayer:
                {
                    return await CheckAndInitializeNewPlayer(ApiClientRequest);
                }
                case APIRequestMethod.RegisterSession:
                {
                    return await RegisterSession(ApiClientRequest);
                }
                case APIRequestMethod.RegisterDevice:
                {
                    return await RegisterDevice(ApiClientRequest);
                }
                case APIRequestMethod.AddActivityToSession:
                {
                    return await AddActivityToSession(ApiClientRequest);
                }
                case APIRequestMethod.AddShotDataToActivity:
                {
                    return await AddShotDataToActivity(ApiClientRequest);
                }
                case APIRequestMethod.FetchSupabaseConfig:
                {
                    return await FetchSupabaseConfig(ApiClientRequest);
                }
                case APIRequestMethod.GetAllEconomyConfig:
                {
                    return await GetAllEconomyConfig(ApiClientRequest);
                }
                case APIRequestMethod.SearchPlayerByName:
                {
                    return await SearchPlayerByName(ApiClientRequest);
                }
                case APIRequestMethod.GetHoleDownloadUrl:
                {
                    return await GetHoleDownloadUrl(ApiClientRequest);
                }
                case APIRequestMethod.GetHoleVersion:
                {
                    return await GetHoleVersion(ApiClientRequest);
                }
                case APIRequestMethod.GetAssetBundleVersions:
                {
                    return await GetAssetBundleVersions(ApiClientRequest);
                }
                case APIRequestMethod.GetAssetBundleUrls:
                {
                    return await GetAssetBundleUrls(ApiClientRequest);
                }
                default:
                {
                    APIServerResponse response = new APIServerResponse(code: ApiClientRequest.code, responseCode: APIResponseCode.Fail_CodeIneligible, time: DateTime.Now, data: "Code has no function.");
                    return response;
                }
            }
        }

        /// <summary>
        /// Get a list of all virtual purchases in economy configuration
        /// </summary>
        /// <param name="context"></param>
        /// <param name="gameApiClient"></param>
        /// <param name="economyConfigurationSDK"></param>
        /// <param name="apiClientRequest">data is a list of string, [0] being configAssignmentHash</param>
        /// <returns></returns>
        private async Task<APIServerResponse> GetEconomyConfigurationRealMoneyPurchases(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }
            
            string configAssignmentHash = parameters[0];

            List<EconomyConfigurationRealMoneyPurchaseModel> result = await EconomyServices.GetEconomyConfigurationRealMoneyPurchases(context, gameApiClient, configAssignmentHash);

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(result);

            return serverResponse;
        }

        private async Task<APIServerResponse> GetPlayerRank(APIClientRequest apiClientRequest)
        {
            var player = await DatabaseService.GetPlayerInfo(context);

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(player.rank);

            return serverResponse;
        }
        
        private async Task<APIServerResponse> GetAssetBundleUrls(APIClientRequest apiClientRequest)
        {
            var request = JsonConvert.DeserializeObject<AssetBundleUrlsRequestData>(apiClientRequest.data);
            
            if (request == null)
            {
                return Fail("Data is null");
            }

            var platForm = request.Platform;
            
            var dataResponse = new AssetBundleUrlsResponseData()
            {
                AssetBundleUrls = await DatabaseService.GetBundleUrls(context, platForm, request.BundlesToFetch)
            };

            return Success(dataResponse);
        }
        
        private async Task<APIServerResponse> GetAssetBundleVersions(APIClientRequest apiClientRequest)
        {
            var request = JsonConvert.DeserializeObject<AssetBundleVersionRequestData>(apiClientRequest.data);
            
            if (request == null)
            {
                return Fail("Data is null");
            }
            
            var platForm = request.Platform;

            var dataResponse = new AssetBundleVersionResponseData
            {
                AssetBundleVersions = await DatabaseService.GetBundleVersions(context, platForm)
            };
            
            return Success(dataResponse);
        }
        
        private async Task<APIServerResponse> GetHoleDownloadUrl(APIClientRequest apiClientRequest)
        {
            var request = JsonConvert.DeserializeObject<HoleRequestData>(apiClientRequest.data);
            
            if (request == null)
            {
                return Fail("Data is null");
            }
            
            var url = await DatabaseService.GetHoleDownloadUrl(context, gameApiClient, request);

            return Success(url);
        }
        
        private async Task<APIServerResponse> GetHoleVersion(APIClientRequest apiClientRequest)
        {
            var request = JsonConvert.DeserializeObject<HoleVersionRequestData>(apiClientRequest.data);
            
            if (request == null)
            {
                return Fail("Data is null");
            }
            
            var version = await DatabaseService.GetHoleVersion(context, gameApiClient, request);
            return Success(version);
        }

        private async Task<APIServerResponse> GetMultipleConfigs(APIClientRequest apiClientRequest)
        {
            var configKeysToGet = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            var isInputValid = configKeysToGet != null && configKeysToGet.Count > 0;
            var data = isInputValid
                ? await RemoteConfigServices.GetConfigs(context, gameApiClient, configKeysToGet) 
                : "Missing Input Keys";
            return new APIServerResponse(
                code: apiClientRequest.code,
                responseCode: isInputValid ? APIResponseCode.Success : APIResponseCode.Fail_CodeIneligible,
                time: DateTime.Now,
                data: data);
        }
        
        private async Task<APIServerResponse> GetMultiplePlayerSaves(APIClientRequest apiClientRequest)
        {
            var configKeysToGet = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            var isInputValid = configKeysToGet != null && configKeysToGet.Count > 0;
            var data = isInputValid
                ? await DatabaseService.GetPlayerSaves(context, configKeysToGet!) 
                : "Missing Input Keys";
            return new APIServerResponse(
                code: apiClientRequest.code,
                responseCode: isInputValid ? APIResponseCode.Success : APIResponseCode.Fail,
                time: DateTime.Now,
                data: data);
        }

        private APIServerResponse GetBucketsInfo(APIClientRequest apiClientRequest)
        {
            var environmentName = string.IsNullOrEmpty(JsonConvert.DeserializeObject<string>(apiClientRequest.data)) 
                ? context.EnvironmentName 
                : JsonConvert.DeserializeObject<string>(apiClientRequest.data);
            var data = JsonConvert.SerializeObject(environmentName switch
            {
                "development" => BucketsInfo.DevBuckets,
                "production" => BucketsInfo.ProdBuckets,
                "qa" => BucketsInfo.QaBuckets,
            });

            return new APIServerResponse(
                code: apiClientRequest.code,
                responseCode: APIResponseCode.Success,
                time: DateTime.Now,
                data: data);
        }
        
        private async Task<APIServerResponse> GetEconomyBallInventoryItems(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }
            

            List<BallInventoryItem> ballInventoryItems = await EconomyServices.GetEconomyConfigurationBallInventoryItems(
                context, 
                gameApiClient,
                isServiceToken: false,
                null,
                (parameters != null) ? parameters[0] : null
            );

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(ballInventoryItems);

            return serverResponse;
        }

        private async Task<APIServerResponse> GetEconomyClubInventoryItems(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }
            
            List<ClubInventoryItem> clubInventoryItems = await EconomyServices.GetEconomyConfigurationClubInventoryItems(
                context, 
                gameApiClient,
                isServiceToken: false,
                null,
                (parameters != null) ? parameters[0] : null)
            ;

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(clubInventoryItems);

            return serverResponse;
        }

        private async Task<APIServerResponse> GetEconomyGearInventoryItems(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }
            

            List<GearInventoryItem> gearInventoryItems = await EconomyServices.GetEconomyConfigurationGearInventoryItems(
                context, 
                gameApiClient, 
                isServiceToken: false, 
                null, 
                (parameters != null) ? parameters[0] : null
            );

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(gearInventoryItems);

            return serverResponse;
        }

        private async Task<APIServerResponse> GetEconomyAvatarItems(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse()
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            
            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Input data is null";
                return response;
            }

            try
            {
                var configAssignmentHash = parameters[0];
                
                var avatarInventoryItems = await EconomyServices.GetEconomyConfigurationAvatarInventoryItems(
                    context: context, 
                    gameApiClient: gameApiClient, 
                    configAssignmentHash: configAssignmentHash ?? null
                );
                
                response.responseCode = APIResponseCode.Success;
                response.data = JsonConvert.SerializeObject(avatarInventoryItems);
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to get Avatar items: {e}";
            }
            
            response.time = DateTime.Now;
            return response;
        }

        private async Task<APIServerResponse> SetPlayerAvatar(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse()
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            
            var avatarId = JsonConvert.DeserializeObject<string>(apiClientRequest.data);
            if (avatarId == null)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Missing Avatar Id";
                return response;
            }
            
            try
            {
                var player = await DatabaseService.From<PlayerEntity>()
                    .Filter("user_id", Constants.Operator.Equals, DatabaseService.GetUserId())
                    .Single();
                if(player == null)
                    throw new Exception($"No player found. UserId: {DatabaseService.GetUserId()}. PlayerId: {context.PlayerId}");

                player.AvatarId = avatarId;
                await DatabaseService.From<PlayerEntity>().Update(player);
                
                response.responseCode = APIResponseCode.Success;
                response.data = JsonConvert.SerializeObject(DataConversionExtensions.ToPlayerInfoModel(player));
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to set Player Avatar: {e}";
            }
            
            response.time = DateTime.Now;
            return response;
        }

        private async Task<APIServerResponse> SetPlayerGadget(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse()
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            
            var playerGadgetSaveData = JsonConvert.DeserializeObject<PlayerGadgetSaveData>(apiClientRequest.data);
            if (playerGadgetSaveData == null)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.UtcNow;
                response.message = "Data is null";
                return response;
            }
            
            try
            {
                var userId = DatabaseService.GetUserId();
                var playerGadget = await DatabaseService.From<PlayerGadgetEntity>()
                    .Filter("user_id", Constants.Operator.Equals, userId)
                    .Single();
                
                Func<Task<ModeledResponse<PlayerGadgetEntity>>> query;
                if (playerGadget == null)
                {
                    playerGadget = playerGadgetSaveData.ToPlayerGadget(userId);
                    query = () => DatabaseService.From<PlayerGadgetEntity>().Insert(playerGadget);
                }
                else
                {
                    playerGadget.UpdateFrom(playerGadgetSaveData);
                    query = () => DatabaseService.From<PlayerGadgetEntity>().Update(playerGadget);
                }
                
                var result = await Utils.ExecuteWithRetry(query);
                if (result == null)
                    throw new Exception($"Could not write PlayerGadget for UserId: {userId}. PlayerGadGet: {JsonConvert.SerializeObject(playerGadget)}");
                
                response.message = $"DATA: {apiClientRequest.data}\nGADGET:{JsonConvert.SerializeObject(playerGadget)}\nRESULT{JsonConvert.SerializeObject(result)}";
                response.responseCode = APIResponseCode.Success;
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to save Player Gadget: {e}";
            }
            
            response.time = DateTime.UtcNow;
            return response;
        }
        
        private async Task<APIServerResponse> GetPlayerInventoryItems(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }
            
            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory
            (
                context,
                gameApiClient,
                isServiceToken: false,
                null,
                (parameters != null) ? parameters[0] : null
            );
            var isCachedDataDirty = false;
            var playerBagDefaultData = await RemoteConfigServices.GetPlayerDefaultData(context, gameApiClient);
            if (playerInventory.isEmpty)
            {
                List<string> itemIds = new()
                {
                    playerBagDefaultData.fragmentClubId1,
                    playerBagDefaultData.fragmentClubId2,
                    playerBagDefaultData.fragmentClubId3,
                    playerBagDefaultData.fragmentClubId4,
                    playerBagDefaultData.fragmentClubId5,
                    playerBagDefaultData.ballId1,
                };

                List<int> quantities = new()
                {
                    playerBagDefaultData.amountFrgamentClubId1,
                    playerBagDefaultData.amountFrgamentClubId2,
                    playerBagDefaultData.amountFrgamentClubId3,
                    playerBagDefaultData.amountFrgamentClubId4,
                    playerBagDefaultData.amountFrgamentClubId5,
                    -1,
                };
                
                
                await EconomyServices.AddItemsToInventory
                (
                    context,
                    gameApiClient,
                    itemIds,
                    quantities,
                    false,
                    context.PlayerId!,
                    null
                ); 
               
                isCachedDataDirty = true;
            }

            foreach (var defaultAvatarId in playerBagDefaultData.avatarIds)
            {
                var ownedAvatar = playerInventory.PlayerAvatarInventoryItems.FirstOrDefault(ownedAvatar => ownedAvatar.id == defaultAvatarId);
                if (ownedAvatar == null)
                {
                    await EconomyServices.AddItemsToInventory
                    (
                        context,
                        gameApiClient,
                        new List<string>{defaultAvatarId},
                        new List<int>{1},
                        false,
                        context.PlayerId!,
                        null
                    );
                    isCachedDataDirty = true;
                }
            }

            if (isCachedDataDirty)
            {
                playerInventory = await EconomyServices.GetPlayerInventory
                (
                    context,
                    gameApiClient,
                    isServiceToken: false,
                    null,
                    (parameters != null) ? parameters[0] : null
                );
            }
            
            APIServerResponse serverResponse = new APIServerResponse();
            
            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(playerInventory);
            
            return serverResponse;
        }

        private async Task<APIServerResponse> UpgradePlayerClubInventoryItem(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }
            
            string configAssignmentHash = parameters[0];
            string itemId = parameters[1];
            
            List<ClubInventoryItem> economyClubs = await EconomyServices.GetEconomyConfigurationClubInventoryItems(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );
            
            CurrenciesResponse playerCurrencies = await EconomyServices.GetPlayerCurrencies(context, gameApiClient);
            
            var clubItem = playerInventory.PlayerClubInventoryItems.Find(item => item.id == itemId);
            var economyClubItem = economyClubs.Find(item => item.id == itemId);

            if (clubItem == null || economyClubItem == null)
            {
                SimpleRespone respone = new SimpleRespone();
                respone.data = "This club does not exist.";
                APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(respone));
                return newReponse;
            }
            
            bool isCanPurchase = false;
            int upgradeCost = 0;
            int upgradeFragment = 0;

            if (clubItem.instanceData.level < economyClubItem.customData.levels.Count)
            {
                upgradeCost = economyClubItem.customData.levels[clubItem.instanceData.level].upgradeCoin;
                upgradeFragment = economyClubItem.customData.levels[clubItem.instanceData.level].upgradeFragment;
                
                if (playerCurrencies.coin >= upgradeCost && clubItem.instanceData.fragment >= upgradeFragment)
                {
                    isCanPurchase = true;
                }
            }

            if (isCanPurchase)
            {
                clubItem.instanceData.level += 1;
                int maxCapacity = economyClubItem.customData.levels[clubItem.instanceData.level - 1].capacity;
                clubItem.instanceData.fragment -= upgradeFragment;

                var respone =  await EconomyServices.UpdatePlayerClubInventoryItem(
                    context,
                    gameApiClient,
                    clubItem.uid,
                    clubItem.instanceData.fragment,
                    maxCapacity,
                    clubItem.instanceData.level,
                    upgradeCost,
                    context.PlayerId!,
                    false,
                    configAssignmentHash
                );

                await _challengeService.TrackUpgradeClub();
                
                APIServerResponse serverResponse = new APIServerResponse();
            
                serverResponse.code = apiClientRequest.code;
                serverResponse.time = DateTime.Now;
                serverResponse.responseCode = APIResponseCode.Success;
                serverResponse.data = JsonConvert.SerializeObject(respone);
            
                return serverResponse;
            }
            else
            {
                SimpleRespone respone = new SimpleRespone();
                respone.data = "User cannot upgrade this club.";
                APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(respone));
                return newReponse;
            }
        }

        private async Task<APIServerResponse> GetCourseConfigByRank(APIClientRequest apiClientRequest)
        {
            List<CourseInfo> courses = await RemoteConfigServices.GetCourseConfigByRank(context, gameApiClient);

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(courses);

            return serverResponse;
        }

        private async Task<APIServerResponse> RefinePlayerClubInventoryItem(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            
            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }

            string configAssignmentHash = parameters[0];
            string itemId = parameters[1];
            int refineAmount = int.Parse(parameters[2]);

            List<ClubInventoryItem> economyClubs = await EconomyServices.GetEconomyConfigurationClubInventoryItems(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            CurrenciesResponse playerCurrencies = await EconomyServices.GetPlayerCurrencies(context, gameApiClient);

            var clubItem = playerInventory.PlayerClubInventoryItems.Find(item => item.id == itemId);
            var economyClubItem = economyClubs.Find(item => item.id == itemId);

            if (clubItem == null || economyClubItem == null)
            {
                SimpleRespone respone = new SimpleRespone();
                respone.data = "This club does not exist.";
                APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(respone));
                return newReponse;
            }

            bool isCanPurchase = false;
            int refineCost = economyClubItem.customData.levels[clubItem.instanceData.level - 1].refineCoin *
                             refineAmount;
            int maxRefineAmount = economyClubItem.customData.levels[clubItem.instanceData.level - 1].capacity -
                                  clubItem.instanceData.capacity;

            if (playerCurrencies.coin >= refineCost && refineAmount > 0 && refineAmount <= maxRefineAmount)
            {
                isCanPurchase = true;
            }

            if (isCanPurchase)
            {
                int capacity = clubItem.instanceData.capacity + refineAmount;

                var respone = await EconomyServices.UpdatePlayerClubInventoryItem(
                    context,
                    gameApiClient,
                    clubItem.uid,
                    clubItem.instanceData.fragment,
                    capacity,
                    clubItem.instanceData.level,
                    refineCost,
                    context.PlayerId!,
                    false,
                    configAssignmentHash
                );

                APIServerResponse serverResponse = new APIServerResponse();

                serverResponse.code = apiClientRequest.code;
                serverResponse.time = DateTime.Now;
                serverResponse.responseCode = APIResponseCode.Success;
                serverResponse.data = JsonConvert.SerializeObject(respone);

                return serverResponse;
            }
            else
            {
                SimpleRespone respone = new SimpleRespone();
                respone.data = "User cannot upgrade this club.";
                APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(respone));
                return newReponse;
            }
        }
        
        private async Task<APIServerResponse> ValidateCurrency(APIClientRequest apiClientRequest)
        {
            var result = false;
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(result),
                code = apiClientRequest.code
            };
            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Data is null";
                return response;
            }

            try
            {
                var currencyId = parameters[0];
                var amount = int.Parse(parameters[1]);
                var configAssignmentHash = parameters[2];

                var consumeCoinResponse = await gameApiClient.EconomyCurrencies.GetPlayerCurrenciesAsync(
                    context,
                    context.AccessToken,
                    context.ProjectId,
                    context.PlayerId!,
                    configAssignmentHash
                );
                var matchingCurrency = consumeCoinResponse.Data.Results.First(currency => currency.CurrencyId == currencyId);
                
                result = matchingCurrency.Balance >= amount;
                response.responseCode = APIResponseCode.Success;
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Failed currency validation: {e}.";
            }

            response.data = JsonConvert.SerializeObject(result);
            response.time = DateTime.Now;
            return response;
        }
        
        private async Task<APIServerResponse> GetPlayersInfo(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Data is null";
                return response;
            }

            try
            {
                var playerIds = JsonConvert.DeserializeObject<List<string>>(parameters[0]);
                var playersInfo = await DatabaseService.GetPlayerInfoInBatch(context, playerIds!.ToArray()); 
                
                response.data = JsonConvert.SerializeObject(playersInfo);
                response.responseCode = APIResponseCode.Success;
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Failed currency validation: {e}.";
            }

            response.time = DateTime.Now;
            return response;
        }
        
        private async Task<APIServerResponse> GetPlayerNames(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Data is null";
                return response;
            }

            try
            {
                var playerIds = JsonConvert.DeserializeObject<List<string>>(parameters[0]);
                var getPlayerNameTasks = playerIds.ToDictionary(playerId => playerId, playerId => gameApiClient.PlayerNamesApi.GetNameAsync(context, context.AccessToken, playerId, false));
                await Task.WhenAll(getPlayerNameTasks.Values);
                var playerNames = getPlayerNameTasks.ToDictionary(pair => pair.Key, pair => pair.Value.Result.Data.Name);
                response.data = JsonConvert.SerializeObject(playerNames);
                response.responseCode = APIResponseCode.Success;
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Failed currency validation: {e}.";
            }

            response.time = DateTime.Now;
            return response;
        }
        
        private async Task<APIServerResponse> CheckDbVersionAndMigrate(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };

            try
            {
                await DatabaseService.CheckVersionAndMigrate(context, gameApiClient);
                response.responseCode = APIResponseCode.Success;
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Failed database migration: {e}.";
            }

            response.time = DateTime.Now;
            return response;
        }
        
        private async Task<APIServerResponse> SetPlayerName(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            
            var newName = JsonConvert.DeserializeObject<string>(apiClientRequest.data);
            if (newName == null)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.UtcNow;
                response.message = "Missing Avatar Id";
                return response;
            }
            
            try
            {
                var player = await DatabaseService.From<PlayerEntity>()
                    .Filter("user_id", Constants.Operator.Equals, DatabaseService.GetUserId())
                    .Single();
                if(player == null)
                    throw new Exception($"No player found. UserId: {DatabaseService.GetUserId()}. PlayerId: {context.PlayerId}");
                
                player.PlayerName = newName;
                var updateDatabaseTask = DatabaseService.From<PlayerEntity>().Update(player);
                var updateNameTask = gameApiClient.PlayerNamesApi.UpdateNameAsync(context, context.AccessToken, context.PlayerId!, new UpdateNameRequest(newName));

                await Task.WhenAll(updateNameTask, updateDatabaseTask);
                
                response.responseCode = APIResponseCode.Success;
                response.data = JsonConvert.SerializeObject(DataConversionExtensions.ToPlayerInfoModel(player));
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to set Player name: {e}";
            }
            
            response.time = DateTime.UtcNow;
            return response;
        }
        
        private async Task<APIServerResponse> CheckAndInitializeNewPlayer(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            
            try
            {
                //Do nothing, the database initialization has already done the job. This is just a fake function to call.
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to set Player name: {e}";
            }
            
            response.time = DateTime.UtcNow;
            return response;
        }

        private async Task<APIServerResponse> RegisterSession(APIClientRequest apiClientRequest)
        {
            SessionEntity sessionEntity = new SessionEntity(DatabaseService.GetUserId());

            await DatabaseService.From<SessionEntity>().Insert(sessionEntity);

            await _challengeService.RegisterChallenge();

            APIServerResponse serverResponse = new APIServerResponse
            {
                code = apiClientRequest.code,
                time = DateTime.Now,
                responseCode = APIResponseCode.Success,
                data = JsonConvert.SerializeObject(sessionEntity.Uuid)
            };

            return serverResponse;
        }
        
        private async Task<APIServerResponse> RegisterDevice(APIClientRequest apiClientRequest)
        {
            try
            {
                var response = await CustomServices.Process(apiClientRequest);
                return ConvertToClientResponse(response);
            }
            catch (Exception e)
            {
                return Fail(e.Message);
            }
        }

        private async Task<APIServerResponse> AddActivityToSession(APIClientRequest apiClientRequest)
        {
            ClientActivity clientActivity = JsonConvert.DeserializeObject<ClientActivity>(apiClientRequest.data)!;

            ActivityEntity activityEntity = new ActivityEntity(
                userId: DatabaseService.GetUserId(),
                sessionUuid: clientActivity.SessionUuid,
                gameMode: clientActivity.GameMode,
                courseId: clientActivity.CourseId,
                holeIds: clientActivity.HoleIds,
                rankId: clientActivity.RankId,
                additionalData: clientActivity.AdditionalData
            );

            var insertActivityTask = Utils.ExecuteWithRetry(() => DatabaseService.From<ActivityEntity>().Insert(activityEntity));

            await _challengeService.TrackPlayModeGame(clientActivity.GameMode);

            await Task.WhenAll(insertActivityTask);

            APIServerResponse serverResponse = new APIServerResponse
            {
                code = apiClientRequest.code,
                time = DateTime.Now,
                responseCode = APIResponseCode.Success,
                data = JsonConvert.SerializeObject(activityEntity.Uuid)
            };

            return serverResponse;
        }

        private async Task<APIServerResponse> FetchSupabaseConfig(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data)!;

            SupabaseConfigList supabaseConfig = new SupabaseConfigList();

            foreach (string parameter in parameters)
            {
                switch (parameter)
                {
                    case SupabaseConfigList.HoleInfo:
                        {
                            var result = await DatabaseService.From<HoleEntity>().Get();
                            List<HoleEntity> holes = result.Models;

                            supabaseConfig.AddConfig(parameter, JsonConvert.SerializeObject(holes));

                            break;
                        }
                    case SupabaseConfigList.CourseList:
                        {
                            var query = await DatabaseService.From<CourseEntity>()
                                .Select("*, course_hole_link(*, hole(*))")
                                .Get();

                            List<CourseEntity> courseList = query.Models;

                            CourseList courseListModel = new CourseList();
                            courseListModel.course_list = new List<Course>();

                            foreach (var courseItem in courseList)
                            {
                                Course newCourse = new Course();
                                newCourse.course_id = courseItem.Uuid;
                                newCourse.course_name = courseItem.Name;
                                newCourse.courseType = courseItem.Type;

                                newCourse.holes = new List<Hole>();

                                foreach (CourseHoleLinkEntity link in courseItem.CourseHoleLinks)
                                {
                                    Hole newHole = new Hole();
                                    newHole.hole_guid = link.HolesEntity.Guid;
                                    newHole.hole_number = link.HoleNumber;

                                    newCourse.holes.Add(newHole);
                                }

                                courseListModel.course_list.Add(newCourse);
                            }

                            supabaseConfig.AddConfig(parameter, JsonConvert.SerializeObject(courseListModel));

                            break;
                        }
                }
            }

            APIServerResponse serverResponse = new APIServerResponse
            {
                code = apiClientRequest.code,
                time = DateTime.Now,
                responseCode = APIResponseCode.Success,
                data = JsonConvert.SerializeObject(supabaseConfig)
            };

            return serverResponse;
        }

        private async Task<APIServerResponse> AddShotDataToActivity(APIClientRequest apiClientRequest)
        {
            List<GolfShotDataModel> golfShots = JsonConvert.DeserializeObject<List<GolfShotDataModel>>(apiClientRequest.data)!;

            await DatabaseService.AddShotDataToActivity(golfShots);
            
            _challengeService.TrackPerformShot(golfShots);
            
            APIServerResponse serverResponse = new APIServerResponse
            {
                code = apiClientRequest.code,
                time = DateTime.Now,
                responseCode = APIResponseCode.Success,
                data = JsonConvert.SerializeObject("Success")
            };

            return serverResponse;
        }

        /// <summary>
        /// An API to fetch a compilation list of all economy configuration (cloud code)
        /// </summary>
        /// <param name="apiClientRequest"></param>
        /// <returns></returns>
        private async Task<APIServerResponse> GetAllEconomyConfig(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data)!;

            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code, responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }

            List<Task> tasks = new List<Task>();

            Task<List<ClubInventoryItem>> clubInventoryTask = EconomyServices.GetEconomyConfigurationClubInventoryItems(
                context,
                gameApiClient,
                isServiceToken: false,
                null,
                (parameters != null) ? parameters[0] : null
            );

            Task<List<BallInventoryItem>> ballInventoryTask = EconomyServices.GetEconomyConfigurationBallInventoryItems(
                context,
                gameApiClient,
                isServiceToken: false,
                null,
                (parameters != null) ? parameters[0] : null
            );

            Task<List<GearInventoryItem>> gearInventoryTask = EconomyServices.GetEconomyConfigurationGearInventoryItems(
                context,
                gameApiClient,
                isServiceToken: false,
                null,
                (parameters != null) ? parameters[0] : null
            );

            Task<List<AvatarInventoryItem>> avatarInventoryTask = EconomyServices.GetEconomyConfigurationAvatarInventoryItems(
                context: context,
                gameApiClient: gameApiClient,
                configAssignmentHash: (parameters != null) ? parameters[0] : null
            );

            Task<List<EconomyConfigurationVirtualPurchaseModel>> virtualPurchasesTask = EconomyServices.GetEconomyConfigurationVirtualPurchases(context, gameApiClient, (parameters != null) ? parameters[0] : null);

            Task<List<EconomyConfigurationRealMoneyPurchaseModel>> realMoneyPurchasesTask = EconomyServices.GetEconomyConfigurationRealMoneyPurchases(context, gameApiClient, (parameters != null) ? parameters[0] : null);

            tasks.Add(clubInventoryTask);
            tasks.Add(ballInventoryTask);
            tasks.Add(gearInventoryTask);
            tasks.Add(avatarInventoryTask);
            tasks.Add(virtualPurchasesTask);
            tasks.Add(realMoneyPurchasesTask);

            await Task.WhenAll(tasks);

            List<ClubInventoryItem> clubInventoryItems = await clubInventoryTask;
            List<BallInventoryItem> ballInventoryItems = await ballInventoryTask;
            List<GearInventoryItem> gearInventoryItems = await gearInventoryTask;
            List<AvatarInventoryItem> avatarInventoryItems = await avatarInventoryTask;
            List<EconomyConfigurationVirtualPurchaseModel> virtualPurchases = await virtualPurchasesTask;
            List<EconomyConfigurationRealMoneyPurchaseModel> realMoneyPurchases = await realMoneyPurchasesTask;

            EconomyAllConfiguration economyAllConfiguration = new EconomyAllConfiguration
            {
                clubInventoryItems = clubInventoryItems,
                ballInventoryItems = ballInventoryItems,
                gearInventoryItems = gearInventoryItems,
                avatarInventoryItems = avatarInventoryItems,
                virtualPurchases = virtualPurchases,
                realMoneyPurchases = realMoneyPurchases
            };

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(economyAllConfiguration);

            return serverResponse;
        }
        
        private async Task<APIServerResponse> SearchPlayerByName(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };
            
            var searchName = JsonConvert.DeserializeObject<string>(apiClientRequest.data);
            if (searchName == null)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.UtcNow;
                response.message = "Missing Search Name";
                return response;
            }
            
            try
            {
                var playerEntities = (await DatabaseService.From<PlayerPublic>()
                    .Filter("player_name", Constants.Operator.ILike, $"%{searchName}%")
                    .Get()).Models;
                var responsePlayers = playerEntities.Select(playerEntity => playerEntity.ToPlayerResponse());
                response.responseCode = APIResponseCode.Success;
                response.data = JsonConvert.SerializeObject(responsePlayers);
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to Search Player by name: {e}";
            }
            
            response.time = DateTime.UtcNow;
            return response;
        }
    }
}
