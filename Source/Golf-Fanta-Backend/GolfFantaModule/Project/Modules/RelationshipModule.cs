using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.DTOs;
using GolfFantaModule.CustomService.Interfaces;
using GolfFantaModule.Supabase.Services.Interfaces;
using GolfFantaModule.UnityGameServices.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Unity.Services.CloudCode.Apis;
using Unity.Services.CloudCode.Core;
using Unity.Services.Friends.Model;

namespace GolfFantaModule.Modules;

public class RelationshipModule : APIModule
{
    public RelationshipModule(ILogger<GolfFantaModule> _logger, IExecutionContext _context, IGameApiClient _gameApiClient, IAdminApiClient _adminApiClient, ICloudSaveServices cloudSaveServices, IRemoteConfigServices remoteConfigServices, IEconomyServices economyServices, IInternalDatabaseService internalDatabaseService, ICustomServices customServices, APIClientRequest apiClientRequest) 
        : base(_logger, _context, _gameApiClient, _adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest)
    {
    }

    public override async Task<APIServerResponse> Process()
    {
        return ApiClientRequest.code switch
        {
            APIRequestMethod.GetRelationships => await GetRelationships(ApiClientRequest),
            _ => Fail("Code has no function.")
        };
    }
    
    private async Task<APIServerResponse> GetRelationships(APIClientRequest apiClientRequest)
    {
        string errorMessage = "";
        try
        {
            var relationships = await gameApiClient.FriendsRelationshipsApi.GetRelationshipsAsync(
                executionContext: context,
                accessToken: context.AccessToken);

            var relationshipDtos = new List<RelationshipDTO>();
            foreach (var relationship in relationships.Data)
            {
                var relationshipDto = new RelationshipDTO();
                var otherPerson = relationship.Members.First(member => member.Id != context.PlayerId);
                relationshipDto.Availability = otherPerson.Presence == null ? UserAvailability.Unknown : otherPerson.Presence.Availability switch
                {
                    ExtendedUserAllOfPresence.AvailabilityEnum.ONLINE => UserAvailability.Online,
                    ExtendedUserAllOfPresence.AvailabilityEnum.OFFLINE => UserAvailability.Offline,
                    ExtendedUserAllOfPresence.AvailabilityEnum.BUSY => UserAvailability.InMatch,
                    _ => UserAvailability.Unknown
                };
                relationshipDto.RelationshipState = relationship.Type switch
                {
                    RelationshipType.FRIEND => RelationshipState.Friend,
                    RelationshipType.BLOCK => RelationshipState.Block,
                    RelationshipType.FRIENDREQUEST => otherPerson.Role == Role.SOURCE
                        ? RelationshipState.ReceivedRequest
                        : RelationshipState.SentRequest,
                    _ => RelationshipState.None
                };
                relationshipDtos.Add(relationshipDto);
            }

            if (relationshipDtos.Count > 0)
            {
                apiClientRequest.data = JsonUtils.ToJson(relationshipDtos);
                return ConvertToClientResponse((await CustomServices.Process(apiClientRequest))!);
            }
            
            var responseData = new GetRelationshipsResponseData();
            responseData.RelationshipDTOs = relationshipDtos;

            return Success(responseData);
        }
        catch (Exception e)
        {
            return Fail($"Fail to Fetch relationships: {e}, Error: {errorMessage}");
        }
    }
}