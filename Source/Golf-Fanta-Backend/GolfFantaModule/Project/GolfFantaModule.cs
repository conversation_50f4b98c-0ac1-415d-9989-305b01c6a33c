using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System;
using GolfFantaCore.Common;
using GolfFantaCore.Common.Utils;
using GolfFantaModule.CustomService.Interfaces;
using Unity.Services.CloudCode.Apis;
using Unity.Services.CloudCode.Core;
using Unity.Services.CloudCode.Shared;
using Unity.Services.Economy.Model;
using Unity.Services.CloudCode.Apis.Admin;
using GolfFantaModule.Modules;
using GolfFantaModule.SDKs;
using GolfFantaModule.Supabase.Services.Implements;
using GolfFantaModule.Supabase.Services.Interfaces;
using GolfFantaModule.UnityGameServices.Services.Implements;
using GolfFantaModule.UnityGameServices.Services.Interfaces;

namespace GolfFantaModule;

public class GolfFantaModule
{
    private readonly ILogger<GolfFantaModule> _logger;

    public GolfFantaModule(ILogger<GolfFantaModule> logger)
    {
        _logger = logger;
    }

    public class ModuleConfig : ICloudCodeSetup
    {
        public void Setup(ICloudCodeConfig config)
        {
            config.Dependencies.AddSingleton(GameApiClient.Create());
            config.Dependencies.AddSingleton(AdminApiClient.Create());
            config.Dependencies.AddSingleton<ICloudSaveServices>(new CloudSaveService());
            config.Dependencies.AddSingleton<IEconomyInventoryServices>(new EcocomyInventoryService());
            config.Dependencies.AddSingleton<IEconomyConfigurationServices>(new EconomyConfigurationService());
            config.Dependencies.AddSingleton<IEconomyServices>(new EconomyService());
            config.Dependencies.AddSingleton<IRemoteConfigServices>(new RemoteConfigService());
            config.Dependencies.AddSingleton<IInternalDatabaseService>(new InternalDatabaseService());
            config.Dependencies.AddSingleton<ICustomServices>(new CustomService.Implements.CustomService());
            config.Dependencies.AddSingleton<IChallengeService>(new ChallengeService());
        }
    }

    [CloudCodeFunction("ClientToServer")]
    public async Task<APIServerResponse> ClientToServer(
        IExecutionContext context,
        IGameApiClient gameApiClient,
        IAdminApiClient adminApiClient,
        ICloudSaveServices cloudSaveServices,
        IEconomyServices economyServices,
        IRemoteConfigServices remoteConfigServices,
        IInternalDatabaseService internalDatabaseService,
        ICustomServices customServices,
        IChallengeService challengeService,
        APIClientRequest apiClientRequest
    )
    {
        var response = new APIServerResponse()
        {
            code = apiClientRequest.code
        };

        var requestId = apiClientRequest.requestId;
        _logger.LogDebug($"START RequestId: {requestId}. Player: {context.PlayerId}. Request: {EnumUtils.GetDisplayName(apiClientRequest.code)}.");
        
        try
        {
            var requestCode = (int)apiClientRequest.code;
            
            APIModule apiModule = requestCode switch
            {
                >= 0 and < 100 => new BagModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, challengeService, apiClientRequest),
                >= 100 and < 200 => new SpecialOfferModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest),
                >= 200 and < 300 => new CommonModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, challengeService, apiClientRequest),
                >= 300 and < 400 => new MatchModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, challengeService, apiClientRequest),
                >= 400 and < 500 => new TestModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest),
                >= 600 and < 700 => new ClosesToPinModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, challengeService, apiClientRequest),
                >= 700 and < 800 => new TournamentModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, challengeService, apiClientRequest),
                >= 900 and < 1000 => new ChallengeModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, challengeService, apiClientRequest),
                >= 1000 and < 1100 => new MessageModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest),
				>= 1100 and < 1200 => new RaceChallengeGameModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest),
                >= 1200 and < 1300 => new TourModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest),
                >= 1300 and < 1400 => new RelationshipModule(_logger, context, gameApiClient, adminApiClient, cloudSaveServices, remoteConfigServices, economyServices, internalDatabaseService, customServices, apiClientRequest),
                _ => throw new ArgumentOutOfRangeException($"Code has no function.: {EnumUtils.GetDisplayName(apiClientRequest.code)}")
            };

            bool isCacheSession = apiClientRequest.code != APIRequestMethod.RegisterSession;
            var userId = await apiModule.Initialize(isCacheSession);
            CustomService.Implements.CustomService.Instance.Initialize(context, userId);
            customServices.Initialize(context, userId);
            challengeService.Initialize(customServices);
            economyServices.Initialize(challengeService);

            response = await apiModule.Process();
        }
        catch (TokenExpiredException e)
        {
            response.responseCode = APIResponseCode.Fail_SessionExpired;
            response.message = e.Message;
        }
        catch (ArgumentOutOfRangeException e)
        {
            response.responseCode = APIResponseCode.Fail_CodeIneligible;
            response.message = e.Message;
        }
        catch (Exception e)
        {
            response.responseCode = APIResponseCode.Fail;
            response.message = e.Message;
        }

        _logger.LogDebug($"FINISH RequestId: {requestId}. Player: {context.PlayerId}. Request: {EnumUtils.GetDisplayName(apiClientRequest.code)}.", response);
        
        response.time = DateTime.UtcNow;
        return response;
    }

    // SPECIAL ENDPOINTS

    [CloudCodeFunction("TestFuncton")]
    public async Task TestFunction(IExecutionContext ctx, IGameApiClient gameApiClient,
        IAdminApiClient adminApiClient,
        string playerId)
    {

    }

    [CloudCodeFunction("InitializeNewPlayer")]
    public async Task InitializeNewPlayer(IExecutionContext ctx, 
        IGameApiClient gameApiClient,
        IEconomyServices economyServices,
        IRemoteConfigServices remoteConfigServices,
        string playerId
    )
    {
        // try
        // {
        //     var database = InternalDatabaseSDK.Instance;
        //     var session = await database.SignUp(ctx, gameApiClient, playerId);
        //     
        //     var defaultBag = new PlayerRewardBag(session.User!.Id!)
        //     {
        //         BagConfigId = DefaultData.DefaultPlayerRewardBagId,
        //         ReceivedTime = DateTime.UtcNow
        //     };
        //     await database.From<PlayerRewardBag>().Insert(defaultBag);
        // }
        // catch (ApiException e)
        // {
        //     _logger.LogError("Failed to initialize player data in Cloud Save. Error: {Error}", e.Message);
        //     throw new Exception($"Failed to initialize {playerId} data in Cloud Save. Error: {e.Message}");
        // }
    }

    [CloudCodeFunction("GetInventory")]
    public async Task<string> GetInventory(IExecutionContext ctx, IGameApiClient gameApiClient,
        string configAssignmentHash
    )
    {
        ApiResponse<PlayerInventoryResponse> result = await gameApiClient.EconomyInventory.GetPlayerInventoryAsync(ctx, ctx.AccessToken, ctx.ProjectId,
            ctx.PlayerId!, configAssignmentHash);

        _logger.LogDebug(result.Data.ToJson());

        return result.Data.ToJson();
    }
}


