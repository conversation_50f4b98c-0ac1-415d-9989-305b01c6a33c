using GolfFantaCore.Common.Enums;
using GolfFantaCore.DTOs;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services;

public class RelationshipService(DatabaseContext dbContext)
{
    public async Task<bool> HasSentFriendGift(Guid senderUuid, string receiverUnityPlayerId)
    {
        var receiverEntity = await dbContext.PlayersPublic
            .AsNoTracking()
            .Where(entity => entity.UnityPlayerGuid == receiverUnityPlayerId)
            .Select(entity => new { entity.UserId, entity.UnityPlayerGuid })
            .FirstOrDefaultAsync();

        if (receiverEntity == null)
            throw new KeyNotFoundException($"Player {receiverUnityPlayerId} does not exist");
        
        var utcStart = DateTime.UtcNow.Date;
        var utcEnd   = utcStart.AddDays(1); 
        
        var sentToday = await dbContext.PlayerMessages
            .Where(entity => 
                entity.SenderUuid == senderUuid 
                && entity.ReceiverUuid == receiverEntity.UserId
                && entity.CreatedAt >= utcStart
                && entity.CreatedAt < utcEnd
                && entity.MessageType == MessageType.Attached)
            .AnyAsync();

        return sentToday;
    }
    
    public async Task<List<PlayerLightweightProfile>> GetPlayerLightweightProfiles(params string[] unityPlayerIds)
    {
        if (unityPlayerIds.Length == 0)
            return new List<PlayerLightweightProfile>();

        var playerIdsList = unityPlayerIds.ToList();
        var playerEntities = await dbContext.PlayersPublic
            .AsNoTracking()
            .Where(player => playerIdsList.Contains(player.UnityPlayerGuid))
            .Select(player => new
            {
                player.UserId,
                player.UnityPlayerGuid,
                player.PlayerName,
                player.AvatarId,
                player.Elo
            })
            .ToListAsync();

        return playerEntities.Select(entity =>
                new PlayerLightweightProfile(entity.UserId, entity.UnityPlayerGuid, entity.PlayerName, entity.AvatarId, entity.Elo))
            .ToList();
    }
}