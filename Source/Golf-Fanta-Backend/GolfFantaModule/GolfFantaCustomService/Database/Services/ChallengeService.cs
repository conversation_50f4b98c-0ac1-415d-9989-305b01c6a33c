using GolfFantaCore.Common.Enums;
using GolfFantaCore.Models.Challenge;
using GolfFantaCore.Models.Challenge.Conditions;
using GolfFantaCustomService.Database.Entities;
using GolfFantaCustomService.Utils;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services;

public class ChallengeService(DatabaseContext context)
{
    private DatabaseContext _context = context;

    #region Ultility

    public async Task<List<PlayerChallengeModel>> GetClaimableChallengeModels(string userId)
    {
        List<PlayerChallengeModel> models = new List<PlayerChallengeModel>();
        var playerChallenges= await GetAllPlayerChallengeByUserId(userId);
        foreach (var playerchallenge in playerChallenges)
        {
            var (challengeEntity, challengeTaskEntities, playerTaskEntities) =
                await GetAllChallengeInfoOfUser(userId, playerchallenge.ChallengeUuid.ToString());
            var model = DataConvert.ToPlayerChallengeModel(challengeEntity!, playerchallenge, challengeTaskEntities!,
                playerTaskEntities!);
            if (model.IsClaimable())
            {
                models.Add(model);
            }
        }
        return models;
    }

    public async Task<List<PlayerChallengeModel>> GetActiveChallengeModels(string userId)
    {
        var claimableChallenge = await GetClaimableChallengeModels(userId);
        var result = new List<PlayerChallengeModel>();
        foreach (var challenge in claimableChallenge)
        {
            if (challenge.IsActive())
            {
                result.Add(challenge);
            }
        }
        return result;
    }
    
    public async Task<(ChallengeEntity? challengeEntity, List<ChallengeTaskEntity>? challengeTaskEntities,
        List<PlayerChallengeTaskEntity>? playerChallengeTaskEntities)> GetAllChallengeInfoOfUser(string userId, string challengeUuid)
    {
        var challengeEntities = await _context.Challenges.ToListAsync();
        
        var challengeTaskEntities = await _context.ChallengeTasks.ToListAsync();
        
        var playerChallengeTaskEntities = await GetAllPlayerTaskChallengeByChallengeId(userId, challengeUuid);
        
        return (challengeEntities.Find((entity)=> entity.Uuid.ToString() == challengeUuid),
            challengeTaskEntities.Where(entity => entity.ChallengeUuid.ToString() == challengeUuid).ToList()
            , playerChallengeTaskEntities);
    }

    #endregion

    #region Challenge

    public async Task<List<ChallengeModel>> GetAllActiveChallenge()
    {
        var entities = await _context.Challenges.Where(c => c.Status == EntityStatus.Active).ToListAsync();
        return entities.Select(entity => entity.ToChallengeModel()).ToList();
    }

    public async Task<ChallengeEntity?> GetChallengeById(string id)
    {
        return await _context.Challenges.FindAsync(id);
    }
    
    public async Task<ChallengeEntity?> GetChallengeByUuId(Guid uuid)
    {
        return await _context.Challenges.FindAsync(uuid);
    }

    public async Task AddChallenge(ChallengeEntity entity)
    {
        _context.Challenges.Add(entity);
        await _context.SaveChangesAsync();
    }

    public async Task UpdateChallengeAsync(ChallengeEntity entity)
    {
        _context.Challenges.Update(entity);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteChallengeAsync(string id)
    {
        var entity = await _context.Challenges.FindAsync(id);
        if (entity != null)
        {
            //await DeleteAllChallengeTaskByChallengeId(id);
            _context.Challenges.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }
    
    #endregion

    #region Challenge Tasks
    
    public async Task<List<ChallengeTaskEntity>> GetAllTaskByChallengeId(string id)
    {
        return await _context.ChallengeTasks
            .Where(task => task.ChallengeUuid.ToString() == id).ToListAsync();
    }
    
    public async Task<ChallengeEntity?> GetChallengeTaskById(string id)
    {
        return await _context.Challenges.FindAsync(id);
    }
    
    public async Task AddChallengeTask(ChallengeTaskEntity entity)
    {
        _context.ChallengeTasks.Add(entity);
        await _context.SaveChangesAsync();
    }
    
    public async Task UpdateChallengeTaskAsync(ChallengeTaskEntity entity)
    {
        _context.ChallengeTasks.Update(entity);
        await _context.SaveChangesAsync();
    }
    
    public async Task DeleteChallengeTaskAsyncById(int id)
    {
        var entity = await _context.ChallengeTasks.FindAsync(id);
        if (entity != null)
        {
            _context.ChallengeTasks.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }
    
    public async Task DeleteAllChallengeTaskByChallengeId(string challengeId)
    {
        var challengeTasks = await GetAllTaskByChallengeId(challengeId);
        for (int i = 0; i < challengeTasks.Count; i++)
        {
            _context.ChallengeTasks.Remove(challengeTasks[i]);
            await _context.SaveChangesAsync();
        }
    }
    
    #endregion

    #region Player Challenge

    public async Task<List<PlayerChallengeEntity>> GetAllPlayerChallengeByUserId(string userId)
    {
        var activeChallenges = new List<PlayerChallengeEntity>();
        var data = await _context.PlayerChallenges.ToListAsync();
        var challenges = await _context.Challenges.ToListAsync();
        var playerChallenges = data
                .Where(task => task.UserId == Guid.Parse(userId));
        foreach (var item in playerChallenges)
        {
            var challenge = challenges.Find((entity => entity.Uuid == item.ChallengeUuid));
            if (challenge is { Status: EntityStatus.Active })
            {
                activeChallenges.Add(item);
            }
        }
        return activeChallenges;
    }
    
    public async Task<List<PlayerChallengeEntity>> RegisterPlayerChallenge(string userId)
    {
        var challenges = await GetAllActiveChallenge();

        var userUuid = Guid.Parse(userId);
        
        var playerChallenges = new List<PlayerChallengeEntity>();

        foreach (var challenge in challenges)
        {
            var challengeUuid = Guid.Parse(challenge.Uuid!);

            var existingPlayerChallenge = await _context.PlayerChallenges
                .FirstOrDefaultAsync(pc => pc.UserId == userUuid && pc.ChallengeUuid == challengeUuid);
            
            if (existingPlayerChallenge == null)
            {    
                var conditions = challenge.UnlockConditions;
                bool canAssign = false;
                if (conditions.Count > 0)
                {
                    for (int i = 0; i < conditions.Count; i++)
                    {
                        var condition = conditions[i];
                        if (condition.Type == ChallengeConditionType.Time)
                        {
                            var timeContext = new ChallengeUnlockContext();
                            canAssign = condition.CanUnlock(timeContext);
                        }
                    }
                }
                else
                {
                    canAssign = true;
                }

                if (canAssign)
                {
                    // Add Player Challenge Entity
                    var newPlayerChallenge = new PlayerChallengeEntity
                    {
                        Uuid = Guid.NewGuid(),
                        UserId = userUuid,
                        ChallengeUuid = challengeUuid,
                        CreatedAt = DateTime.UtcNow,
                        ClaimedMilestoneUuids = [],
                    };

                    _context.PlayerChallenges.Add(newPlayerChallenge);
                    playerChallenges.Add(newPlayerChallenge);
                
                    // Add Task Of Challenge
                    var challengeTask = await GetAllTaskByChallengeId(challengeUuid.ToString());
                
                    for (int i = 0; i < challengeTask.Count; i++)
                    {
                        var newPlayerChallengeTask = new PlayerChallengeTaskEntity()
                        {
                            Uuid = Guid.NewGuid(),
                            UserId = userUuid,
                            ChallengeTaskUuid = challengeTask[i].Uuid,
                            PlayerChallengeUuid = newPlayerChallenge.Uuid,
                            CreatedAt = DateTime.UtcNow,
                            Progress = 0,
                            ClaimTime = null
                        };
                        _context.PlayerChallengeTasks.Add(newPlayerChallengeTask);
                    }
                }
            }
        }
        await _context.SaveChangesAsync();
        return playerChallenges;
    }

    public async Task<PlayerChallengeEntity?> GetPlayerChallengeByUuid(string uuid)
    {
        return await _context.PlayerChallenges.FindAsync(uuid);
    }

    public async Task AddPlayerChallenge(PlayerChallengeEntity entity)
    {
        _context.PlayerChallenges.Add(entity);
        await _context.SaveChangesAsync();
    }

    public async Task UpdatePlayerChallengeAsync(PlayerChallengeEntity entity)
    {
        _context.PlayerChallenges.Update(entity);
        await _context.SaveChangesAsync();
    }

    #endregion

    #region Player Challenge Task

    public async Task<List<PlayerChallengeTaskEntity>> GetAllPlayerTaskChallengeByChallengeId(string userId, string challengeUuId)
    {
        var playerChallengeTasks = await _context.PlayerChallengeTasks.ToListAsync();

        var response = new List<PlayerChallengeTaskEntity>();

        foreach (var item in playerChallengeTasks)
        {
           if(item.UserId.ToString() == userId) response.Add(item);   
        }

        return response;
    }
    
    public async Task UpdateChallengeTasks(List<ChallengeTaskModel> taskModels)
    {
        var data = await _context.PlayerChallengeTasks.ToListAsync();

        foreach (var task in taskModels)
        {
            var taskEntity = data.FirstOrDefault((entity => entity.Uuid == Guid.Parse(task.PlayerChallengeTaskUuid)));
            if (taskEntity != null)
            {
                taskEntity.Progress = task.Progress;
                _context.PlayerChallengeTasks.Update(taskEntity);
            }
        }
        await _context.SaveChangesAsync();
    }
    
    public async Task<List<ChallengeTaskModel>> ClaimChallengeTasksReward(List<string> taskUuids)
    {
        var response = new List<ChallengeTaskModel>();

        var challengesEntities = await _context.Challenges.ToListAsync();
        
        var challengeTasks = await _context.ChallengeTasks.ToListAsync();
        
        var playerTasksEntities = await _context.PlayerChallengeTasks.ToListAsync();
        
        foreach (var uuid in taskUuids)
        {
            var playerTaskEntity = playerTasksEntities.FirstOrDefault((entity => entity.Uuid == Guid.Parse(uuid)));
            if (playerTaskEntity != null)
            {
                var challengeTaskEntity = challengeTasks.FirstOrDefault((entity => entity.Uuid == playerTaskEntity.ChallengeTaskUuid));
                if (challengeTaskEntity != null)
                {
                    var taskModel = DataConvert.ToChallengeTaskModel(challengeTaskEntity, playerTaskEntity);
                    var challengeEntity =
                        challengesEntities.FirstOrDefault(item => item.Uuid == challengeTaskEntity.ChallengeUuid);
                    if (challengeEntity != null)
                    {
                        if (taskModel.IsUnlock(challengeEntity.ChallengeType) && taskModel.Progress >= taskModel.RequiredProgress)
                        {
                            playerTaskEntity.ClaimTime = DateTime.UtcNow;
                            response.Add(DataConvert.ToChallengeTaskModel(challengeTaskEntity, playerTaskEntity));
                            _context.PlayerChallengeTasks.Update(playerTaskEntity);
                        }
                    }
                }
            }
        }
        await _context.SaveChangesAsync();
        return response;
    }
    
    public async Task<List<ChallengeMilestone>> ClaimChallengeMilestoneReward(string challengeId, string userId, List<string> claimUuids)
    {
        var response = new List<ChallengeMilestone>();

        var challenges = await _context.Challenges.ToListAsync();

        var currentChallenge = challenges.FirstOrDefault(entity => entity.Uuid.ToString() == challengeId);

        if (currentChallenge == null)
        {
            return response;
        }

        var milestones = currentChallenge.RewardMilestones;
        
        var challengeTasks = await _context.ChallengeTasks.ToListAsync();

        var playerTaskEntities = await GetAllPlayerTaskChallengeByChallengeId(userId: userId, challengeId);

        var playerChallenges = await _context.PlayerChallenges.ToListAsync();

        var playerTasksModels = new List<ChallengeTaskModel>();

        foreach (var entity in playerTaskEntities)
        {
            var challengeTask = challengeTasks.FirstOrDefault((c => c.Uuid == entity.ChallengeTaskUuid));
            if (challengeTask != null)
            {
                playerTasksModels.Add(DataConvert.ToChallengeTaskModel(challengeTask, entity));
            }
        }

        var point = 0;
        
        foreach (var item in playerTasksModels)
        {
            if (item.IsFinished)
            {
                point += item.ProgressReward;
            }
        }   

        var playerChallenge = playerChallenges.FirstOrDefault(c => c.UserId.ToString() == userId);

        var milestonesUuids = milestones.Select(c => c.Uuid).ToList();
        
        foreach (var uuid in claimUuids.Select(Guid.Parse))
        {
            if (milestonesUuids.Contains(uuid))
            {
                var milestoneInfo = milestones.Find(c => c.Uuid == uuid);
                if (milestoneInfo!.MilestonePoint <= point)
                {
                    var claimMilestones = playerChallenge!.ClaimedMilestoneUuids.Select(c => c.Uuid).ToList();
                    if (!claimMilestones.Contains(uuid))
                    {
                        var claimMilestoneEntity = new ChallengeClaimedMilestoneEntity()
                        {
                            Uuid = uuid,
                            ClaimTime = DateTime.UtcNow
                        };
                        playerChallenge!.ClaimedMilestoneUuids.Add(claimMilestoneEntity);
                        claimMilestones.Add(uuid);
                        response.Add(DataConvert.ToChallengeMilestoneModel(milestoneInfo, claimMilestoneEntity.ClaimTime)!);
                    }
                }
            }
        }

        _context.PlayerChallenges.Update(playerChallenge!);
        
        await _context.SaveChangesAsync();
        return response;
    }

    #endregion
}