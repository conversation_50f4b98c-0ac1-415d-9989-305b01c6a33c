using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Entities;

[Table("player")]
public class PlayerEntity : PlayerBaseEntity
{
    [Key]
    [Column("id")]
    public int Id { get; set; }
    
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [Column("last_modified")]
    public DateTime LastModified { get; set; }
    
    public PlayerEntity(){}
}

[Keyless]
public class PlayerPublicEntity : PlayerBaseEntity { }

public abstract class PlayerBaseEntity
{
    [Column("user_id")]
    public Guid UserId { get; set; }
    
    [Column("unity_player_id")]
    public string UnityPlayerGuid { get; set; }

    [Column("player_name")]
    public string PlayerName { get; set; }
    
    [Column("avatar_id")]
    public string? AvatarId { get; set; }
    
    [Column("elo")]
    public int Elo { get; set; }
    
    [Column("rank_id")]
    public string? RankId { get; set; }
    
    [Column("total_h2h_games")]
    public int TotalH2hGames { get; set; }
    
    [Column("total_h2h_games_won")]
    public int TotalH2hGamesWon { get; set; }
    
    [Column("h2h_win_streak")]
    public int H2hWinStreak { get; set; }
    
    [Column("longest_h2h_win_streak")]
    public int LongestH2hWinStreak { get; set; }
    
    [Column("longest_drive_distance")]
    public float LongestDrive { get; set; }
    
    [Column("total_hole_in_one")]
    public int HolesInOne { get; set; }
    
    [Column("total_albatross")]
    public int Albatrosses { get; set; }
    
    [Column("total_eagle")]
    public int Eagles { get; set; }
    
    [Column("total_birdie")]
    public int Birdies { get; set; }
    
    [Column("total_h2h_coin_won")]
    public int TotalH2hCoinsWon { get; set; }
    
    [Column("total_h2h_elo")]
    public int TotalH2hElo { get; set; }
    
    [Column("highest_h2h_rank")]
    public string? HighestH2hRankId { get; set; }
}