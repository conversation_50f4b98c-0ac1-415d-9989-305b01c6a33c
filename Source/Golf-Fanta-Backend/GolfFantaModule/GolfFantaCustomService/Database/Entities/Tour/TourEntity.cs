using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.CommonEntities;
using GolfFantaCore.Models.Challenge;
using GolfFantaCore.Models.Common;

namespace GolfFantaCustomService.Database.Entities.Tour;

[Table("tour")]
public class TourEntity
{
    [Key]
    [Column("id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Column("uuid")]
    public Guid Uuid { get; set; }
    
    [Column("name_key")]
    public string NameKey { get; set; }
    
    [Column("description_key")]
    public string DescriptionKey { get; set; }
    
    [Column("course_uuid")]
    public Guid CourseUuid { get; set; }
    
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [Column("last_modified")]
    public DateTime LastModified { get; set; }
    
    [Column("game_mode")]
    public GameMode GameMode { get; set; }
    
    [Column("banner_address")]
    public string BannerAddress { get; set; }
    
    [Column("leaderboard_id")]
    public string LeaderboardId { get; set; }
    
    [Column("status")]
    public EntityStatus Status { get; set; }
    
    [Column("conditions")]
    public List<ConditionEntity> Conditions { get; set; }
    
    [Column("difficulties")]
    public List<TourDifficultEntity> Difficulties { get; set; }
    
    [Column("entry_fees")]
    public List<TourEntryFeeEntity> EntryFees { get; set; }
    
    [Column("parameters")]
    public string Data { get; set; }
}