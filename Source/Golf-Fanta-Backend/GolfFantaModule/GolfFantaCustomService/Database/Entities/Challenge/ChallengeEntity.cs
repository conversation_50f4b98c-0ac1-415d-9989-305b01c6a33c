using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.CommonEntities;
using GolfFantaCore.DTOs;
using GolfFantaCore.Models.Challenge;

namespace GolfFantaCustomService.Database.Entities;

[Table("challenge")]
public class ChallengeEntity
{
    [Key]
    [Column("id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Column("uuid")]
    public  Guid Uuid { get; set; }

    [Column("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [Column("last_modified")]
    public DateTime LastModified { get; set; }
    
    [Column("status")]
    public EntityStatus Status { get; set; }
    
    [Column("conditions")]
    public List<ConditionEntity> Conditions { get; set; }
    
    [Column("name_key")]
    public string NameKey { get; set; }
    
    [Column("description_key")]
    public string DescriptionKey { get; set; }
    
    [Column("challenge_type")]
    public ChallengeType ChallengeType { get; set; }
    
    //Json of milestones and corresponding rewards
    [Column("reward_milestones")]
    public List<ChallengeRewardMilestoneEntity> RewardMilestones { get; set; }
    
    [Column("active_duration")]
    public int ActiveDuration { get; set; }
    
    [Column("total_duration")]
    public int TotalDuration { get; set; }
    
    [Column("duration_mode")]
    public DurationMode DurationMode { get; set; }
    
    public ChallengeEntity() {}
}

public class ChallengeRewardMilestoneEntity
{
    public Guid Uuid { get; set; }
    public int MilestonePoint { get; set; }
    public List<EconomyItemEntity> Rewards { get; set; }
}