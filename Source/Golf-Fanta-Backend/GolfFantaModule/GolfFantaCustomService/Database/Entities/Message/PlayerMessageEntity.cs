using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using GolfFantaCore.Common.Enums;

namespace GolfFantaCustomService.Database.Entities.Message;

[Table("player_message")]
public class PlayerMessageEntity
{
    [Key]
    [Column("id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Column("uuid")]
    public Guid Uuid { get; set; }
    
    [Column("receiver_uuid")]
    public Guid ReceiverUuid { get; set; }
    
    [Column("sender_uuid")]
    public Guid? SenderUuid { get; set; }
    
    [Column("message_uuid")]
    public Guid? MessageUuid { get; set; }

    [Column("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [Column("last_modified")]
    public DateTime LastModified { get; set; }
    
    [Column("title")]
    public string Title { get; set; }
    
    [Column("content")]
    public string Content { get; set; }
    
    [Column("message_type")]
    public MessageType MessageType { get; set; }
    
    [Column("message_data")]
    public string MessageData { get; set; }
    
    [Column("response_data")]
    public string ResponseData { get; set; }
    
    [Column("expire_at")]
    public DateTime ExpireAt { get; set; }
    
    [Column("read_at")]
    public DateTime? ReadAt { get; set; }
    
    [Column("claimed_at")]
    public DateTime? ClaimedAt { get; set; }
    
    [Column("delete_at")]
    public DateTime? DeletedAt { get; set; }
}