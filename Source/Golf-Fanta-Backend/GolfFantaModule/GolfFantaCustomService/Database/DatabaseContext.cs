using System.Text.Json;
using GolfFantaCore.Common;
using GolfFantaCore.CommonEntities;
using GolfFantaCustomService.Database.Entities;
using GolfFantaCustomService.Database.Entities.Message;
using GolfFantaCustomService.Database.Entities.Tour;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database;

public class DatabaseContext : DbContext
{
    public DbSet<PlayerEntity> Players { get; set; }
    public DbSet<PlayerPublicEntity> PlayersPublic { get; set; }
    public DbSet<ChallengeEntity> Challenges { get; set; }
    public DbSet<ChallengeTaskEntity> ChallengeTasks { get; set; }
    public DbSet<PlayerChallengeEntity> PlayerChallenges { get; set; }
    public DbSet<PlayerChallengeTaskEntity> PlayerChallengeTasks { get; set; }
    
    public DbSet<MessageEntity> Messages { get; set; }
    public DbSet<PlayerMessageEntity> PlayerMessages { get; set; }
    public DbSet<PlayerDeviceEntity> PlayerDevices { get; set; }
    public DbSet<TourEntity> Tours { get; set; }

    public DatabaseContext(DbContextOptions<DatabaseContext> options)
        : base(options)
    {
    }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        //Player Public View
        modelBuilder.Entity<PlayerPublicEntity>()
            .ToView("player_public_view", schema: "public");
        
        // Challenge Entity
        modelBuilder.Entity<ChallengeEntity>()
            .Property(e => e.RewardMilestones)
            .HasColumnType("json")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonUtils.ParseFromJsonString<List<ChallengeRewardMilestoneEntity>>(v)
            );
        modelBuilder.Entity<ChallengeEntity>()
            .Property(c => c.Conditions)
            .HasColumnType("json");
        
        modelBuilder.Entity<ChallengeEntity>()
            .Property(c => c.Status)
            .HasConversion<string>();
        
        modelBuilder.Entity<ChallengeEntity>()
            .Property(c => c.DurationMode)
            .HasConversion<string>();
        
        modelBuilder.Entity<ChallengeEntity>()
            .Property(c => c.ChallengeType)
            .HasConversion<string>();
        
        // Challenge Task Entity
        modelBuilder.Entity<ChallengeTaskEntity>()
            .Property(e=> e.Parameters)
            .HasColumnType("json")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => System.Text.Json.JsonSerializer.Deserialize<List<ParameterEntity>>(v, (JsonSerializerOptions)null)
            );
        
        modelBuilder.Entity<ChallengeTaskEntity>()
            .Property(c => c.OtherReward)
            .HasColumnType("json");
        
        modelBuilder.Entity<PlayerChallengeEntity>()
            .Property(c => c.ClaimedMilestoneUuids)
            .HasColumnType("json");
        
        //Message 
        modelBuilder.Entity<MessageEntity>()
            .Property(e => e.Data)
            .HasColumnType("json");

        
        // Player Message
        modelBuilder.Entity<PlayerMessageEntity>()
            .Property(e => e.MessageData)
            .HasColumnType("json");
        modelBuilder.Entity<PlayerMessageEntity>()
            .Property(e => e.ResponseData)
            .HasColumnType("json");
        modelBuilder.Entity<PlayerMessageEntity>()
            .Property(e => e.MessageType)
            .HasConversion<string>();

        modelBuilder.Entity<PlayerDeviceEntity>()
            .Property(e => e.DeviceInfo)
            .HasColumnType("json");
        
        // Tour Entity
        modelBuilder.Entity<TourEntity>()
            .Property(e => e.Difficulties)
            .HasColumnType("json")
            .HasConversion(
                v => JsonUtils.ToJson(v),
                v => JsonUtils.ParseFromJsonString<List<TourDifficultEntity>>(v)!
            );
        
        modelBuilder.Entity<TourEntity>()
            .Property(e => e.EntryFees)
            .HasColumnType("json")
            .HasConversion(
                v => JsonUtils.ToJson(v),
                v => JsonUtils.ParseFromJsonString<List<TourEntryFeeEntity>>(v)!
            );
        
        modelBuilder.Entity<TourEntity>()
            .Property(e => e.Conditions)
            .HasColumnType("json")
            .HasConversion(
                v => JsonUtils.ToJson(v),
                v => JsonUtils.ParseFromJsonString<List<ConditionEntity>>(v)!
            );

        modelBuilder.Entity<TourEntity>()
            .Property(e => e.Data)
            .HasColumnType("json");
        
        modelBuilder.Entity<TourEntity>()
            .Property(c => c.Status)
            .HasConversion<string>();
        
        modelBuilder.Entity<TourEntity>()
            .Property(c => c.GameMode)
            .HasConversion<string>();
    }
}