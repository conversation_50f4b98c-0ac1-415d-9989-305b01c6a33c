using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.Common.Extensions;
using GolfFantaCore.Common.Utils;
using GolfFantaCore.CommonEntities;
using GolfFantaCore.DTOs;
using GolfFantaCore.Models;
using GolfFantaCore.Models.Challenge;
using GolfFantaCore.Models.Challenge.Conditions;
using GolfFantaCore.Models.Common;
using GolfFantaCore.Models.Message;
using GolfFantaCore.Models.PlayerData;
using GolfFantaCore.Models.Tour;
using GolfFantaCustomService.Database.Entities;
using GolfFantaCustomService.Database.Entities.Message;
using GolfFantaCustomService.Database.Entities.Tour;
using Newtonsoft.Json.Linq;

namespace GolfFantaCustomService.Utils;

public static class DataConvert
{
    #region Entity

    public static ConditionEntity ToChallengeConditionEntity(ChallengeConditionDto dto)
    {
        return new ConditionEntity
        {
            Parameters = dto.Parameters.Select((pair => new ParameterEntity(){ Key = pair.Key, Value = pair.Value})).ToList(),
            ConditionDescriptionKey = dto.ConditionDescriptionKey,
            ConditionType = dto.ConditionType,
            ConditionNameKey = dto.ConditionNameKey
        };
    }

    public static ChallengeEntity ToChallengeEntity(CreateChallengeDto dto)
    {
        var challenge = new ChallengeEntity
        {
            Uuid = Guid.NewGuid(),
            CreatedAt = DateTime.UtcNow,
            NameKey = dto.NameKey,
            DescriptionKey = dto.DescKey,
            RewardMilestones = [],
            Conditions = []
        };
        if (Enum.IsDefined(typeof(EntityStatus), dto.Status))
        {
            challenge.Status = (EntityStatus)dto.Status;
        }
        if (Enum.TryParse<ChallengeType>(dto.ChallengeType, out var challengeType))
        {
            challenge.ChallengeType = challengeType;
        }
        for (int i = 0; i < dto.Milestones.Count; i++)
        {
            var entity = new ChallengeRewardMilestoneEntity
            {
                MilestonePoint = dto.Milestones[i].MilestonePoint,
                Rewards = dto.Milestones[i].Rewards.Select(c=>c.ToEconomyItemEntity()).ToList(),
                Uuid = Guid.NewGuid(),
            };
            challenge.RewardMilestones .Add(entity);
        }

        foreach (var condition in dto.Conditions)
        {
            var entity = DataConvert.ToChallengeConditionEntity(condition);
            challenge.Conditions.Add(entity);
        }
        return challenge;
    }

    public static EconomyItemEntity ToEconomyItemEntity(this EconomyItem model)
    {
        return new EconomyItemEntity()
        {
            ItemId =  model.ItemId,
            Quantity = model.Quantity
        };
    }
    
    public static ChallengeTaskEntity ToChallengeTaskEntity(ChallengeTaskRequest dto)
    {
        var challengeTask = new ChallengeTaskEntity
        {
            Uuid = Guid.NewGuid(),
            CreatedAt = DateTime.UtcNow,
            NameKey = dto.NameKey,
            DescriptionKey = dto.DescriptionKey,
            TaskType = dto.TaskType,
            RequiredProgress = dto.RequiredProgress,
            UnlockedOnSecond = dto.UnlockedOnSecond,
            ProgressReward = dto.ProgressReward,
            Parameters = [],
            Action = dto.Action,
            OtherReward = dto.OtherReward.Select(c=>c.ToEconomyItemEntity()).ToList()
        };

        foreach (var key in dto.Parameters.Keys)
        {
            if(dto.Parameters.TryGetValue(key, out var value))
            {
                var taskParamEntity = new ParameterEntity
                {
                    Key = key,
                    Value = value
                };
                challengeTask.Parameters.Add(taskParamEntity);
            }
        }
        
        return challengeTask;
    }
    
    public static MessageEntity ToMessageEntity(this MessageModel messageModel)
    {
        return new MessageEntity()
        {
            Id = messageModel.Id,
            Uuid = messageModel.Uuid,
            CreatedAt = messageModel.CreatedAt,
            LastModified = messageModel.LastModified,
            Title = messageModel.Title,
            Content = messageModel.Content,
            MessageType = EnumUtils.GetDisplayName(messageModel.MessageType),
            DeliveryType = EnumUtils.GetDisplayName(messageModel.DeliveryType),
            Data = messageModel.Data,
            DeletedAt = messageModel.DeletedAt
        };
    }

    public static PlayerMessageEntity ToPlayerMessageEntity(this PlayerMessageModel playerMessage)
    {
        return new PlayerMessageEntity()
        {
            Id = playerMessage.Id,
            Uuid = playerMessage.Uuid,
            MessageUuid = playerMessage.MessageUuid,
            ReceiverUuid = playerMessage.ReceiverUuid,
            SenderUuid = playerMessage.SenderUuid,
            CreatedAt = playerMessage.CreatedAt,
            LastModified = playerMessage.LastModified,
            Title = playerMessage.Title,
            Content = playerMessage.Content,
            MessageType = playerMessage.MessageType,
            MessageData = playerMessage.MessageData.ToString()!,
            ResponseData =  playerMessage.ResponseData.ToString()!,
            ExpireAt = playerMessage.ExpireAt,
            ReadAt = playerMessage.ReadAt,
            ClaimedAt = playerMessage.ClaimedAt,
            DeletedAt = playerMessage.DeletedAt
        };
    }

    public static PlayerDeviceEntity ToPlayerDeviceEntity(this PlayerDeviceModel playerDevice)
    {
        return new PlayerDeviceEntity()
        {
            Id = playerDevice.Id,
            Uuid = playerDevice.Uuid,
            UserId = playerDevice.UserId,
            DeviceToken = playerDevice.DeviceToken,
            DeviceInfo = JsonUtils.ToJson(playerDevice.DeviceInfo),
            LastSeen = playerDevice.LastSeen,
            PushEnabled = playerDevice.PushEnabled,
            CreatedAt = playerDevice.CreatedAt,
            LastModified = playerDevice.LastModified
        };
    }
    
    #endregion

    #region Model
    
    public static PlayerChallengeModel ToPlayerChallengeModel(ChallengeEntity challengeEntity,
        PlayerChallengeEntity playerChallengeEntity,
        List<ChallengeTaskEntity> taskEntities, List<PlayerChallengeTaskEntity> playerChallengeTaskEntities)
    {
        var model = new PlayerChallengeModel
        {
            Challenge = challengeEntity.ToChallengeModel(),
            PlayerChallengeUuid = playerChallengeEntity.ChallengeUuid.ToString()!,
            PlayerChallengeCreatedAt = playerChallengeEntity.CreatedAt,
            PlayerChallengeId = playerChallengeEntity.Id,
            PlayerChallengeLastModified = playerChallengeEntity.LastModified,
            Milestones = [],
            Tasks = []
        };
        
        
        for (int i = 0; i < challengeEntity.RewardMilestones.Count; i++)
        {
            var isClaimedMilestone = playerChallengeEntity.ClaimedMilestoneUuids.Select(c => c.Uuid.ToString()).ToList()
                .Contains(challengeEntity.RewardMilestones[i].Uuid.ToString());
            
            var claimedMilestoneEntity = playerChallengeEntity.ClaimedMilestoneUuids
                .FirstOrDefault(item => item.Uuid.ToString() == challengeEntity.RewardMilestones[i].Uuid.ToString());
            
            var milestoneMode = ToChallengeMilestoneModel(challengeEntity.RewardMilestones[i],
                isClaimedMilestone ? claimedMilestoneEntity!.ClaimTime : null);
            
            if (milestoneMode != null)
            {
                model.Milestones.Add(milestoneMode);
            }
        }
        
        for (int i = 0; i < playerChallengeTaskEntities.Count; i++)
        {
            var taskEntity = taskEntities.Find((challengeTaskEntity => challengeTaskEntity.Uuid == playerChallengeTaskEntities[i].ChallengeTaskUuid));
            var taskModel = ToChallengeTaskModel(taskEntity!, playerChallengeTaskEntities[i]);
            model.Tasks.Add(taskModel);
        }
        
        return model;
    }
    
    public static ChallengeModel ToChallengeModel(this ChallengeEntity challengeEntity)
    {
        var result = new ChallengeModel()
        {
            Id = challengeEntity.Id,
            Uuid = challengeEntity.Uuid.ToString()!,
            ChallengeCreatedAt = challengeEntity.CreatedAt,
            Status = challengeEntity.Status,
            DurationMode = challengeEntity.DurationMode,
            ActiveDuration = challengeEntity.ActiveDuration,
            TotalDuration = challengeEntity.TotalDuration,
            NameKey = challengeEntity.NameKey,
            DescriptionKey = challengeEntity.DescriptionKey,
            ChallengeType = challengeEntity.ChallengeType,
        };
        
        var conditions = new List<IChallengeUnlockCondition>();
        for (int i = 0; i < challengeEntity.Conditions.Count; i++)
        {
            var conditionModel = challengeEntity.Conditions[i].ToChallengeUnlockConditionModel();
            if (conditionModel != null)
            {
                conditions.Add(conditionModel);
            }
        }
        
        result.SetUnlockConditions(conditions);

        return result;
    }
    
    public static ChallengeTaskModel ToChallengeTaskModel(ChallengeTaskEntity entity, PlayerChallengeTaskEntity playerChallengeTaskEntity)
    {
        var taskType = Enum.TryParse<ChallengeTaskType>(entity.TaskType, out var parsedStatus);
        return new ChallengeTaskModel()
        {
            ChallengeTaskId = entity.Id,
            ChallengeTaskUuid = entity.Uuid.ToString(),
            ChallengeTaskCreatedAt = entity.CreatedAt,
            ChallengeTaskLastModified = entity.LastModified,
            PlayerChallengeTaskId = playerChallengeTaskEntity.Id,
            PlayerChallengeTaskUuid = playerChallengeTaskEntity.Uuid.ToString(),
            PlayerChallengeTaskCreatedAt = playerChallengeTaskEntity.CreatedAt,
            PlayerChallengeTaskLastModified = playerChallengeTaskEntity.LastModified,
            Progress = playerChallengeTaskEntity.Progress,
            OtherReward = entity.OtherReward.Select(c=> c.ToEconomyItem()).ToList(),
            NameKey = entity.NameKey,
            DescriptionKey = entity.DescriptionKey,
            Type = parsedStatus,
            Parameters = entity.Parameters.ToDictionary(p => p.Key, p => p.Value),
            Action = JsonUtils.ParseFromJsonString<TaskAction>(entity.Action) ?? new TaskAction(),
            UnlockedOnSecond = entity.UnlockedOnSecond,
            RequiredProgress = entity.RequiredProgress,
            ProgressReward = entity.ProgressReward,
            ClaimedTime = playerChallengeTaskEntity.ClaimTime,
        };
    }

    public static EconomyItem ToEconomyItem(this EconomyItemEntity entity)
    {
        return new EconomyItem()
        {
            ItemId = entity.ItemId,
            Quantity = entity.Quantity
        };
    }

    public static IChallengeUnlockCondition? ToChallengeUnlockConditionModel(
        this ConditionEntity entity)
    {
        if (Enum.TryParse<ChallengeConditionType>(entity.ConditionType, out var parsedStatus))
        {
            return UnlockConditionFactory.CreateCondition(parsedStatus, entity.Parameters.ToDictionary(p => p.Key, p => p.Value));
        }
        else
        {
            return null;
        }
    }

    public static ChallengeMilestone? ToChallengeMilestoneModel(ChallengeRewardMilestoneEntity entity, DateTime? claimTime)
    {
        return new ChallengeMilestone()
        {
            MilestoneUuid = entity.Uuid.ToString(),
            MilestonePoint = entity.MilestonePoint,
            Reward = entity.Rewards.Select(c=>c.ToEconomyItem()).ToList(),
            ClaimedTime = claimTime
        };
    }
    
    public static MessageModel ToMessageModel(this MessageEntity messageEntity)
    {
        return new MessageModel()
        {
            Id = messageEntity.Id,
            Uuid = messageEntity.Uuid,
            Title = messageEntity.Title,
            Content = messageEntity.Content,
            MessageType = EnumUtils.ParseDisplayName<MessageType>(messageEntity.MessageType),
            DeliveryType = EnumUtils.ParseDisplayName<DeliveryType>(messageEntity.DeliveryType),
            Data = messageEntity.Data,
            CreatedAt = messageEntity.CreatedAt,
            LastModified = messageEntity.LastModified,
            DeletedAt = messageEntity.DeletedAt
        };
    }
    
    public static PlayerMessageModel ToPlayerMessageModel(this PlayerMessageEntity messageEntity)
    {
        return new PlayerMessageModel()
        {
            Id = messageEntity.Id,
            Uuid = messageEntity.Uuid,
            ReceiverUuid = messageEntity.ReceiverUuid,
            SenderUuid = messageEntity.SenderUuid,
            CreatedAt = messageEntity.CreatedAt,
            LastModified = messageEntity.LastModified,
            Title = messageEntity.Title,
            Content = messageEntity.Content,
            MessageType = messageEntity.MessageType,
            MessageData = JsonUtils.ParseFromJsonString<JObject>(messageEntity.MessageData)!,
            ResponseData = JsonUtils.ParseFromJsonString<JObject>(messageEntity.ResponseData)!,
            ExpireAt = messageEntity.ExpireAt,
            ReadAt = messageEntity.ReadAt,
            ClaimedAt = messageEntity.ClaimedAt,
            DeletedAt = messageEntity.DeletedAt
        };
    }

    public static PlayerModel ToPlayerModel(this PlayerEntity playerEntity)
    {
        return new PlayerModel()
        {
            UserId = playerEntity.UserId,
            UnityPlayerGuid = playerEntity.UnityPlayerGuid,
            PlayerName = playerEntity.PlayerName,
            AvatarId = playerEntity.AvatarId,
            Elo = playerEntity.Elo,
            RankId = playerEntity.RankId,
            TotalH2hGames = playerEntity.TotalH2hGames,
            TotalH2hGamesWon = playerEntity.TotalH2hGamesWon,
            H2hWinStreak = playerEntity.H2hWinStreak,
            LongestH2hWinStreak = playerEntity.LongestH2hWinStreak,
            LongestDrive = playerEntity.LongestDrive,
            HolesInOne = playerEntity.HolesInOne,
            Albatrosses = playerEntity.Albatrosses,
            Eagles = playerEntity.Eagles,
            Birdies = playerEntity.Birdies,
            TotalH2hCoinsWon = playerEntity.TotalH2hCoinsWon,
            TotalH2hElo = playerEntity.TotalH2hElo,
            HighestH2hRankId = playerEntity.HighestH2hRankId,
        };
    }
    
    public static PlayerDeviceModel ToPlayerDeviceModel(this PlayerDeviceEntity deviceEntity)
    {
        return new PlayerDeviceModel(deviceEntity.Uuid, deviceEntity.UserId, deviceEntity.DeviceToken,
            deviceEntity.DeviceInfo, deviceEntity.PushEnabled);
    }
    
    public static TourModel ToTourModel(this TourEntity tourEntity)
    {
        return new TourModel()
        {
            Id = tourEntity.Id,
            Uuid = tourEntity.Uuid,
            BannerAddress = tourEntity.BannerAddress,
            LeaderboardId = tourEntity.LeaderboardId,
            CreatedAt = tourEntity.CreatedAt,
            GameMode =  tourEntity.GameMode,
            CourseUuid = tourEntity.CourseUuid,
            NameKey = tourEntity.NameKey,
            DescriptionKey = tourEntity.DescriptionKey,
            Conditions = tourEntity.Conditions.Select(c=> c.ToTourConditionModel()).ToList(),
            Difficulties = tourEntity.Difficulties.Select(c=> c.ToTourDifficultModel()).ToList(),
            EntryFees = tourEntity.EntryFees.Select(c=> c.ToTourEntryFeeModel()).ToList(),
            Data = tourEntity.Data
        };
    }
    
    #endregion
    
    #region UpdateEntity
    
    public static MessageEntity UpdateFromMessageModel(this MessageEntity messageEntity, MessageModel messageModel)
    {
        messageEntity.CreatedAt = messageModel.CreatedAt;
        messageEntity.LastModified = messageModel.LastModified;
        messageEntity.Title = messageModel.Title;
        messageEntity.Content = messageModel.Content;
        messageEntity.MessageType = EnumUtils.GetDisplayName(messageModel.MessageType);
        messageEntity.DeliveryType = EnumUtils.GetDisplayName(messageModel.DeliveryType);
        messageEntity.Data = messageModel.Data;
        messageEntity.DeletedAt = messageModel.DeletedAt;
        return messageEntity;
    }

    public static PlayerMessageEntity UpdateFromPlayerMessageModel(this PlayerMessageEntity playerMessageEntity, PlayerMessageModel playerMessageModel)
    {
        playerMessageEntity.CreatedAt = playerMessageModel.CreatedAt;
        playerMessageEntity.LastModified = playerMessageModel.LastModified;
        playerMessageEntity.ReceiverUuid = playerMessageModel.ReceiverUuid;
        playerMessageEntity.SenderUuid = playerMessageModel.SenderUuid;
        playerMessageEntity.CreatedAt = playerMessageModel.CreatedAt;
        playerMessageEntity.LastModified = playerMessageModel.LastModified;
        playerMessageEntity.Title = playerMessageModel.Title;
        playerMessageEntity.Content = playerMessageModel.Content;
        playerMessageEntity.MessageType = playerMessageModel.MessageType;
        playerMessageEntity.MessageData = playerMessageModel.MessageData.ToString();
        playerMessageEntity.ResponseData = playerMessageModel.ResponseData.ToString();
        playerMessageEntity.ExpireAt = playerMessageModel.ExpireAt;
        playerMessageEntity.ReadAt = playerMessageModel.ReadAt;
        playerMessageEntity.ClaimedAt = playerMessageModel.ClaimedAt;
        playerMessageEntity.DeletedAt = playerMessageModel.DeletedAt;
        return playerMessageEntity;
    }

    public static PlayerDeviceEntity UpdateFromDeviceModel(this PlayerDeviceEntity playerDeviceEntity, PlayerDeviceModel playerDeviceModel)
    {
        playerDeviceEntity.DeviceToken = playerDeviceModel.DeviceToken;
        playerDeviceEntity.DeviceInfo = JsonUtils.ToJson(playerDeviceModel.DeviceInfo);
        playerDeviceEntity.LastSeen = playerDeviceModel.LastSeen;
        playerDeviceEntity.PushEnabled = playerDeviceModel.PushEnabled;
        return playerDeviceEntity;
    }
    
    #endregion
}