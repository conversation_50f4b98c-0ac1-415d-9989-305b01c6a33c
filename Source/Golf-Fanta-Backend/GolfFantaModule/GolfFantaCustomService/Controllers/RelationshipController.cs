using GolfFantaCore.Common;
using GolfFantaCore.DTOs;
using GolfFantaCustomService.Database;
using GolfFantaCustomService.Database.Services;

namespace GolfFantaCustomService.Controllers;

public class RelationshipController(DatabaseContext databaseContext) : BaseController
{
    private CommonService _commonService = new(databaseContext);
    private RelationshipService _relationshipService = new(databaseContext);
    
    public override async Task<CustomAPIServerResponse> Handle(CustomAPIClientRequest request)
    {
        switch (request.Code)
        {
            case APIRequestMethod.GetRelationships: return await GetRelationships(request);
            
            default: return Fail("Invalid func code.");
        }
    }
    
    private async Task<CustomAPIServerResponse> GetRelationships(CustomAPIClientRequest request)
    {
        try
        {
            var relationshipDtos = JsonUtils.ParseFromJsonString<List<RelationshipDTO>>(request.Data);
            var userId = request.UserId;
            var userGuid = Guid.Parse(userId);

            for (int i = relationshipDtos.Count-1; i >= 0; i--)
            {
                var playerLightweightProfile = await _commonService.GetPlayerLightweightProfiles(relationshipDtos[i].PlayerId);
                if (playerLightweightProfile == null)
                {
                    relationshipDtos.RemoveAt(i);
                    continue;
                }

                relationshipDtos[i].PlayerId = playerLightweightProfile.UnityPlayerId;
                relationshipDtos[i].PlayerName = playerLightweightProfile.PlayerName;
                relationshipDtos[i].AvatarId = playerLightweightProfile.AvatarId;
                relationshipDtos[i].Elo = playerLightweightProfile.Elo;
                relationshipDtos[i].GiftSent = await _relationshipService.HasSentFriendGift(userGuid, relationshipDtos[i].PlayerId);
            }

            var responseData = new GetRelationshipsResponseData
            {
                RelationshipDTOs = relationshipDtos
            };
            return Success(responseData);
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }
}