using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.Common.Extensions;
using GolfFantaCore.Common.Utils;
using GolfFantaCore.DTOs;
using GolfFantaCore.Models.Challenge.Task;
using GolfFantaCore.Models.Common;
using GolfFantaCore.Models.Economy;
using GolfFantaCustomService.Database;
using GolfFantaCustomService.Database.Entities;
using GolfFantaCustomService.Database.Services;
using GolfFantaCustomService.Utils;
using Newtonsoft.Json;

namespace GolfFantaCustomService.Controllers;

public class ChallengeController(DatabaseContext databaseContext) : BaseController
{
    private ChallengeService _challengeService = new(databaseContext);
    
    public override async Task<CustomAPIServerResponse> Handle(CustomAPIClientRequest request)
    {
        switch (request.Code)
        {
            case APIRequestMethod.GetAllChallenges: return await GetAllChallenges(request);
            case APIRequestMethod.UpdateChallenge: return await UpdateChallenge(request);
            case APIRequestMethod.GetAllChallengeTask: return await GetAllChallengesTask(request);
            case APIRequestMethod.UpdateChallengeTask: return await UpdateChallengeTask(request);
            
            case APIRequestMethod.RegisterPlayerChallenge: return await RegisterPlayerChallenge(request);
            case APIRequestMethod.GetAllPlayerChallenge: return await GetAllUserChallenges(request);
            case APIRequestMethod.ClaimChallengeTaskReward: return await ClaimTaskReward(request);
            case APIRequestMethod.ClaimChallengeMilestoneReward: return await ClaimMilestoneReward(request);
            
            case APIRequestMethod.ChallengeTrackPlayModeGame: return await TrackPlay(request);
            case APIRequestMethod.ChallengeTrackWinGameMode: return await TrackWinGameMode(request);
            case APIRequestMethod.ChallengeTrackOpenBag: return await TrackOpenBag(request);
            case APIRequestMethod.ChallengeTrackSkipBag: return await TrackSkipBag(request);
            case APIRequestMethod.ChallengeTrackUpgradeClub: return await TrackUpgradeClub(request);
            case APIRequestMethod.ChallengeTrackConsumeBall: return await TrackConsumeBall(request);
            case APIRequestMethod.ChallengeTrackHoleResult: return await TrackHoleResult(request);
            case APIRequestMethod.ChallengeTrackReceiveClub: return await TrackReceiveClub(request);
            case APIRequestMethod.ChallengeTrackPerformShot: return await TrackPerformShot(request);
            case APIRequestMethod.ChallengeTrackPerformDistanceToPin: return await TrackPerformDistanceToPin(request);
            case APIRequestMethod.ChallengeTrackHoleResultTieBreakWithGameMode :
                return await TrackHoleResultTieBreakWithGameMode(request);
            
            default: return Fail("Invalid func code.");
        }
    }

    #region Challenge

    // private async Task<CustomAPIServerResponse> CreateChallenge(APIRequest request)
    // {
    //     var challengeData = JsonConvert.DeserializeObject<CreateChallengeDto>(request.Data.ToString()!);
    //     //var challengeData = JsonConvert.DeserializeObject<CreateChallengeDto>(request.objectData.ToString());
    //     try
    //     {
    //         if (challengeData != null)
    //         {
    //             ChallengeEntity entity = DataConvert.ToChallengeEntity(challengeData);
    //             await _challengeService.AddChallenge(entity);
    //             var response = JsonConvert.SerializeObject(entity);
    //             return Success(response, "Create Successfully!");
    //         }
    //         else
    //         {
    //             return Fail("Create Fail!");
    //         }
    //     }
    //     catch (Exception e)
    //     {
    //         return Fail(e.Message);
    //     }
    // }

    private async Task<CustomAPIServerResponse> GetAllChallenges(CustomAPIClientRequest request)
    {
        try
        {
            var entities = await _challengeService.GetAllActiveChallenge();
            return Success(entities,  "All Challenges");
        }
        catch (Exception e)
        {
            return Fail("Get Fail!");
        }
        
    }

    private async Task<CustomAPIServerResponse> UpdateChallenge(CustomAPIClientRequest request)
    {
        var challengeData = JsonConvert.DeserializeObject<CreateChallengeDto>(request.Data);
        //var challengeData = JsonConvert.DeserializeObject<CreateChallengeDto>(request.objectData.ToString());
        try
        {
            if (challengeData != null)
            {
                var existEntity = await _challengeService.GetChallengeById(request.Data);
                if (existEntity != null)
                {
                    var entity = DataConvert.ToChallengeEntity(challengeData);
                    entity.Id = existEntity.Id;
                    await _challengeService.UpdateChallengeAsync(entity);
                    return Success(entity, "Update Success");
                }
                else
                {
                    return Fail("Update Fail!");
                }
            }
            else
            {
                return Fail("Update Fail!");
            }
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }

    #endregion

    #region Challenge Task

    // private async Task<CustomAPIServerResponse> CreateChallengeTask(APIRequest request)
    // {
    //     var challengeTaskData = JsonConvert.DeserializeObject<ChallengeTaskRequest>(request.Data.ToString()!);
    //     try
    //     {
    //         if (true)
    //         {
    //             ChallengeTaskEntity entity = DataConvert.ToChallengeTaskEntity(challengeTaskData);
    //             if (!Guid.TryParse(challengeTaskData.ChallengeUuid, out var parsedGuid))
    //                     return Fail("Invalid UUID format!");
    //             entity.ChallengeUuid = parsedGuid;
    //             await _challengeService.AddChallengeTask(entity);
    //             return Success(JsonConvert.SerializeObject(entity), "Create Successfully!");
    //         }
    //         else
    //         {
    //             return Fail("Create Fail!");
    //         }
    //     }
    //     catch (Exception e)
    //     {
    //         return Fail(e.Message);
    //     }
    // }
    
    private async Task<CustomAPIServerResponse> GetAllChallengesTask(CustomAPIClientRequest request)
    {
        var challengeId = request.Data;
        try
        {
            var entities = await _challengeService.GetAllTaskByChallengeId(challengeId);
            return Success(entities,  "All Challenges");
        }
        catch (Exception e)
        {
            return Fail("Get Fail!");
        }
    }
    
    private async Task<CustomAPIServerResponse> UpdateChallengeTask(CustomAPIClientRequest request)
    {
        var challengeData = JsonConvert.DeserializeObject<ChallengeTaskRequest>(request.Data);
        //var challengeData = JsonConvert.DeserializeObject<ChallengeTaskRequest>(request.objectData.ToString());
        try
        {
            if (challengeData != null)
            {
                var existEntity = await _challengeService.GetChallengeTaskById(request.Data);
                if (existEntity != null)
                {
                    var entity = DataConvert.ToChallengeTaskEntity(challengeData);
                    entity.Id = existEntity.Id;
                    await _challengeService.UpdateChallengeTaskAsync(entity);
                    return Success(entity, "Update Success");
                }
                else
                {
                    return Fail("Update Fail!");
                }
            }
            else
            {
                return Fail("Update Fail!");
            }
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }

    #endregion

    #region Player Challenge

    private async Task<CustomAPIServerResponse> RegisterPlayerChallenge(CustomAPIClientRequest request)
    {
        var userId = request.UserId;
        
        try
        {
            var response= await _challengeService.RegisterPlayerChallenge(userId);
            return await GetAllUserChallenges(request);
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }
    
    private async Task<CustomAPIServerResponse> GetAllUserChallenges(CustomAPIClientRequest request)
    {
        var userId = request.UserId;
        
        try
        {
            var models= await _challengeService.GetClaimableChallengeModels(userId);
            return Success(models.Select(c=> c.ToChallengeResponse()).ToList(), "Get All Successfully!");
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }

    private async Task<CustomAPIServerResponse> ClaimTaskReward(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }
        
        var userId = request.UserId;

        var challengeUuid = parameters[0];
        
        var taskUuIds = JsonConvert.DeserializeObject<List<string>>(parameters[1])!;
        
        try
        {
            ChallengeClaimResponse response = new ChallengeClaimResponse();
            
            var taskModels= await _challengeService.ClaimChallengeTasksReward(taskUuIds);
            
            var challengeModels= await _challengeService.GetClaimableChallengeModels(userId);
            
            var challengeData = challengeModels.Select(c => c.ToChallengeResponse()).ToList();

            response.Challenge = challengeData.FirstOrDefault(item => item.Uuid == challengeUuid)!;
            
            response.Items = new List<ChallengeRewardResponse>();
            
            foreach (var taskModel in taskModels)
            {
                foreach (var reward in taskModel.OtherReward)
                {
                    var existingItem = response.Items.FirstOrDefault(item => item.ItemId == reward.ItemId);

                    if (existingItem == null)
                    {
                        var item = new ChallengeRewardResponse()
                        {
                            ItemId = reward.ItemId,
                            Quantity = reward.Quantity,
                        };
                        response.Items.Add(item);
                    }
                    else
                    {
                        existingItem.Quantity += reward.Quantity;
                    }
                }
            }
            return Success(response, "Claim Task Successfully!");
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }
    
    private async Task<CustomAPIServerResponse> ClaimMilestoneReward(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        var challengeUuid = parameters[0];
        
        var milestoneUuid = JsonConvert.DeserializeObject<List<string>>(parameters[1])!;

        try
        {
            ChallengeClaimResponse response = new ChallengeClaimResponse();
            
            var milestoneModels= await _challengeService.ClaimChallengeMilestoneReward(challengeUuid, userId, milestoneUuid);
            
            var challengeModels= await _challengeService.GetClaimableChallengeModels(userId);
                        
            var challengeData = challengeModels.Select(c => c.ToChallengeResponse()).ToList();

            response.Challenge = challengeData.FirstOrDefault(item => item.Uuid == challengeUuid)!;
            
            response.Items = new List<ChallengeRewardResponse>();
            
            foreach (var taskModel in milestoneModels)
            {
                foreach (var reward in taskModel.Reward)
                {
                    var existingItem = response.Items.FirstOrDefault(item => item.ItemId == reward.ItemId);

                    if (existingItem == null)
                    {
                        var item = new ChallengeRewardResponse()
                        {
                            ItemId = reward.ItemId,
                            Quantity = reward.Quantity,
                        };
                        response.Items.Add(item);
                    }
                    else
                    {
                        existingItem.Quantity += reward.Quantity;
                    }
                }
            }
            return Success(response, "Claim Milestone Successfully!");
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }

    #endregion

    #region Update Progress Tasks

    private async Task<CustomAPIServerResponse> TrackPlay(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        if (!Enum.IsDefined(typeof(GameMode), parameters[0]))
        {
            return Fail("Not Found Game Mode");
        }
        
        var gameMode = (GameMode)Enum.Parse(typeof(GameMode), parameters[0].ToString());
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.Play);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskPlayContext()
                    {
                        CurrentProgress = (float)task.Progress,
                        AddValue = 1,
                        GameMode = gameMode
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackWinGameMode(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        if (!Enum.IsDefined(typeof(GameMode), parameters[0]))
        {
            return Fail("Not Found Game Mode");
        }
        
        var gameMode = (GameMode)Enum.Parse(typeof(GameMode), parameters[0].ToString());
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.WinMode);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskWinModeContext()
                    {
                        CurrentProgress = (float)task.Progress,
                        AddValue = 1,
                        GameMode = gameMode
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackOpenBag(CustomAPIClientRequest request)
    {
        var userId = request.UserId;
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.OpenBag);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskBaseContext()
                    {
                        CurrentProgress = (float)task.Progress,
                        AddValue = 1,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackSkipBag(CustomAPIClientRequest request)
    {
        var userId = request.UserId;
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.SkipBag);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskBaseContext()
                    {
                        CurrentProgress = (float)task.Progress,
                        AddValue = 1,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackUpgradeClub(CustomAPIClientRequest request)
    {
        var userId = request.UserId;
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.UpgradeClub);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskBaseContext()
                    {
                        CurrentProgress = (float)task.Progress,
                        AddValue = 1,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackConsumeBall(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        int.TryParse(parameters[0], out var amount);
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.ConsumeByType);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskConsumeItemByTypeContext()
                    {
                        ItemType = InventoryType.Ball,
                        CurrentProgress = (float)task.Progress,
                        AddValue = amount,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackHoleResult(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        int.TryParse(parameters[0], out var point);
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.HoleResult);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskHoleResultContext()
                    {
                        ShotPoint = point,
                        CurrentProgress = (float)task.Progress,
                        AddValue = 1,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        return Fail("Track Fail");
    }

    private async Task<CustomAPIServerResponse> TrackReceiveClub(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        var rarity = (InventoryRarity)Enum.Parse(typeof(InventoryRarity),parameters[0]);
        
        int.TryParse(parameters[1], out var amount);
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.GetItemByTypeWithRarity);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskBuyItemWithRarityContext()
                    {
                        Rarity = rarity,
                        ItemType  = InventoryType.Club,
                        CurrentProgress = (float)task.Progress,
                        AddValue = amount,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackPerformShot(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        var shotModels = JsonConvert.DeserializeObject<List<GolfShotDataModel>>(parameters[0]);

        var perfectShots = shotModels
            .Select(c => EnumUtils.ParseDisplayName<ShotRating>(c.ShotRating)).ToList();
        
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var distanceTasks = challenge.GetTasksByType(ChallengeTaskType.PerformShotWithDistance);
            foreach (var task in distanceTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskPerformShotWithDistanceContext()
                    {
                        Distances  = shotModels!.ToList().Select(c=> c.TotalDistance).ToList(),
                        CurrentProgress = (float)task.Progress,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            var perfectTasks = challenge.GetTasksByType(ChallengeTaskType.PerformShotWithRating);

            foreach (var task in perfectTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskPerformShotWithRatingContext()
                    {
                        Ratings = perfectShots,
                        CurrentProgress = (float)task.Progress,
                    };
                    task.UpdateProgress(playContext);
                }
            }

            await _challengeService.UpdateChallengeTasks(distanceTasks.Concat(perfectTasks).ToList());
            
            return Success("Tracking Successfully!");
        }
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackPerformDistanceToPin(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        var userId = request.UserId;
        
        var gameMode = (GameMode)Enum.Parse(typeof(GameMode),parameters[0]);

        var values = JsonConvert.DeserializeObject<List<float>>(parameters[1]);
                
        var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
        foreach (var challenge in availableChallengesModels)
        {
            var playTasks = challenge.GetTasksByType(ChallengeTaskType.PerformDistanceToPin);
            foreach (var task in playTasks)
            {
                if (task.IsUnlock(challenge.GetChallengeType()))
                {
                    var playContext = new TaskHoleResultWithGameModeContext()
                    {
                        GameMode = gameMode,
                        Values  = values!,
                        CurrentProgress = (float)task.Progress,
                    };
                    task.UpdateProgress(playContext);
                }
            }
            await _challengeService.UpdateChallengeTasks(playTasks);
            return Success("Tracking Successfully!");
        }
        return Fail("Track Fail");
    }
    
    private async Task<CustomAPIServerResponse> TrackHoleResultTieBreakWithGameMode(CustomAPIClientRequest request)
    {
        List<string> parameters = JsonConvert.DeserializeObject<List<string>>(request.Data)!;
            
        if (parameters == null)
        {
            return Fail("Empty Data");
        }

        try
        {
            var userId = request.UserId;
            var gameMode = (GameMode)Enum.Parse(typeof(GameMode),parameters[0]);

            var values = JsonConvert.DeserializeObject<List<float>>(parameters[1]);
                
            var availableChallengesModels = await _challengeService.GetActiveChallengeModels(userId);
        
            foreach (var challenge in availableChallengesModels)
            {
                var playTasks = challenge.GetTasksByType(ChallengeTaskType.HoleResultTieBreakWithGameMode);
                foreach (var task in playTasks)
                {
                    if (task.IsUnlock(challenge.GetChallengeType()))
                    {
                        var playContext = new TaskHoleResultWithGameModeContext()
                        {
                            GameMode = gameMode,
                            Values  = values!,
                            CurrentProgress = (float)task.Progress,
                        };
                        task.UpdateProgress(playContext);
                    }
                }
                await _challengeService.UpdateChallengeTasks(playTasks);
                return Success("Tracking Successfully!");
            }
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
        return Fail("Track Fail");
    }
    
    #endregion
}