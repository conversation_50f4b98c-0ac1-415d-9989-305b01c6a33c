using GolfFantaCore.Common.Enums;

namespace GolfFantaCore.DTOs;

public class GetRelationshipsRequestData
{
}
    
public class GetRelationshipsResponseData
{
    public List<RelationshipDTO> RelationshipDTOs;
}

public class RelationshipDTO
{
    public string PlayerId;
    public string PlayerName;
    public string AvatarId;
    public int Elo;
    public RelationshipState RelationshipState;
    public UserAvailability Availability;
    public bool GiftSent;
}