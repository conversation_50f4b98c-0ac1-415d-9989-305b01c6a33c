namespace GolfFantaCore.DTOs;

[Serializable]
public class ChallengeResponse
{
    public int Id { get; set; }
    
    public string Uuid { get; set; }

    public List<ChallengeConditionResponse> Conditions { get; set; }
    
    public string NameKey { get; set; }
    
    public string DescriptionKey { get; set; }
    
    public string ChallengeType { get; set; }
    
    public List<ChallengeMilestoneResponse> Milestones { get; set; }
    
    public int CurrentProgressPoint { get; set; }
    
    public List<ChallengeTaskResponse> ChallengeTasks { get; set; }
    
    public DateTime StartTime { get; set; }
    
    public DateTime? ActiveTime { get; set; }
    
    public DateTime? EndTime { get; set; }
}

public class ChallengeClaimResponse
{
    public ChallengeResponse Challenge;
    public List<ChallengeRewardResponse> Items;
}