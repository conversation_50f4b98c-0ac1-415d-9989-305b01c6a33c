using GolfFantaCore.Models.Ranking;

namespace GolfFantaCore.Models.Challenge.Conditions;

public class ChallengeRankCondition : IChallengeUnlockCondition
{
    public ChallengeConditionType Type => ChallengeConditionType.RequiredElo;
    public string ConditionNameKey { get; set; } = string.Empty;
    public string ConditionDescriptionKey { get; set; } = string.Empty;

    public Rank RequiredRank { get; set; }
    
    public ChallengeRankCondition(RankConditionParameters parameters)
    {
        RequiredRank = new Rank(parameters.RequiredRankId);
    }
    
    public bool CanUnlock(ChallengeUnlockContext unlockContext)
    {
        throw new NotImplementedException();
    }

    public object ToObject()
    {
        return new
        {
            RequiredRank = RequiredRank.Id,
        };
    }
}