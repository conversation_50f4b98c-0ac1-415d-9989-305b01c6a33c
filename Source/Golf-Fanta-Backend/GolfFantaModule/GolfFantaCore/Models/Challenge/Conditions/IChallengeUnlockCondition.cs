namespace GolfFantaCore.Models.Challenge.Conditions;

public enum ChallengeConditionType
{
    Time,
    Level,
    Coin,
    RequiredElo,
    Maturity
}

public interface IChallengeUnlockCondition
{
    public ChallengeConditionType Type { get; }
    public string ConditionNameKey { get; }
    public string ConditionDescriptionKey { get; }
    public bool CanUnlock(ChallengeUnlockContext unlockContext);
    public object ToObject();
}