using GolfFantaCore.Common.Enums;
using GolfFantaCore.Models.Challenge.Conditions;

namespace GolfFantaCore.Models.Challenge;

public class ChallengeModel
{
    public int Id { get; set; }
    
    public string? Uuid { get; set; }
    
    public DateTime ChallengeCreatedAt { get; set; }
    
    public EntityStatus Status { get; set; }
    
    public DurationMode DurationMode { get; set; }
    
    public string? NameKey { get; set; }
    
    public string? DescriptionKey { get; set; }
    
    public ChallengeType ChallengeType { get; set; }
    
    public int ActiveDuration { get; set; }
    
    public int TotalDuration { get; set; }
    
    private List<IChallengeUnlockCondition> _unlockConditions = new();
    public IReadOnlyList<IChallengeUnlockCondition> UnlockConditions => _unlockConditions;
    
    private ChallengeTimeCondition? _timeCondition => _unlockConditions.FirstOrDefault(condition => condition.Type == ChallengeConditionType.Time) == null 
        ? null : (ChallengeTimeCondition)_unlockConditions.First(condition => condition.Type == ChallengeConditionType.Time);

    public DateTime? ChallengeStartTime => _timeCondition?.StartDateTime;
    
    public DateTime? ChallengeEndTime => _timeCondition?.EndDateTime;
    
    public void SetUnlockConditions(List<IChallengeUnlockCondition> inputs)
    {
        _unlockConditions = inputs;
    }

    public bool IsUnlock(ChallengeUnlockContext context)
    {
        return UnlockConditions.All(condition => condition.CanUnlock(context));;
    }
}