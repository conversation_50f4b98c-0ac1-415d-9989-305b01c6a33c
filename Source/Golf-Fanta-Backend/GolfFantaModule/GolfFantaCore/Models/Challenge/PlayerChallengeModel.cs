using GolfFantaCore.Common.Enums;
using GolfFantaCore.Models.Challenge.Conditions;

namespace GolfFantaCore.Models.Challenge;

public class PlayerChallengeModel
{
    public ChallengeModel Challenge;
    public int PlayerChallengeId { get; set; }
    public string? PlayerChallengeUuid { get; set; }
    public DateTime PlayerChallengeCreatedAt { get; set; }
    public DateTime PlayerChallengeLastModified { get; set; }
    
    public List<ChallengeTaskModel> Tasks { get; set; }
    public List<ChallengeMilestone> Milestones { get; set; }
    
    public int CurrentProgressPoints;
    
    public DateTime? GetPlayerChallengeEndTime
    {
        get
        {
            switch (Challenge.DurationMode)
            {
                case DurationMode.Global:
                case DurationMode.Individual:
                    return PlayerChallengeCreatedAt.AddSeconds(Challenge.ActiveDuration);
                default:
                    return null;
            }
        }
    }

    public DateTime? GetPlayerChallengeActiveTime
    {
        get
        {
            switch (Challenge.DurationMode)
            {
                case DurationMode.Global:
                    return PlayerChallengeCreatedAt.AddSeconds(Challenge.ActiveDuration) < Challenge.ChallengeEndTime
                        ? PlayerChallengeCreatedAt.AddSeconds(Challenge.ActiveDuration)
                        : Challenge.ChallengeEndTime;
                case DurationMode.Individual:
                    return PlayerChallengeCreatedAt.AddSeconds(Challenge.ActiveDuration);
                default:
                    return null;
            }
        }
    }
    
    public bool IsClaimable()
    {
        if (Challenge.DurationMode == DurationMode.Infinity) return true;
            return DateTime.UtcNow < GetPlayerChallengeEndTime;
    }
    
    public bool IsActive()
    {
        if (Challenge.DurationMode == DurationMode.Infinity) return true;
        return Challenge.Status == EntityStatus.Active && DateTime.UtcNow >= PlayerChallengeCreatedAt && DateTime.UtcNow <= GetPlayerChallengeActiveTime;
    }
    
    public List<ChallengeTaskModel> GetTasksByType(ChallengeTaskType type)
    {
        return Tasks.Where(task => task.Type == type).ToList();
    }

    public int CalculateProgress()
    {
        return CurrentProgressPoints = Tasks.Where(task => task.RewardClaimed).Sum(task => task.ProgressReward);
    }

    public ChallengeType GetChallengeType()
    {
        return Challenge.ChallengeType;
    }
}