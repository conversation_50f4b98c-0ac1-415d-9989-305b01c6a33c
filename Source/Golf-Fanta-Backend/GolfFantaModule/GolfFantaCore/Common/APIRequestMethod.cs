namespace GolfFantaCore.Common
{
    // each module can contains 100 codes
    public enum APIRequestMethod
    {
        // bag module
        GetDailyBagStatus = 0,
        GetDailyBagReward = 1,
        GetPlayerBagInfo = 2,
        GetPlayerBag = 3,

        // special offer module
        GetSpecialOfferInformation = 100,
        BuySpecialOffer = 101,
        GetSpecialOfferStatus = 102,
        GetEconomyConfigurationVirtualPurchases = 103,
        BuyVirtualPurchase = 104,
        GetShopConfig = 105,
        BuyRealMoneyPurchase = 106,

        // common module
        GetPlayerRank = 200,
        GetEconomyBallInventoryItems = 201,
        GetEconomyClubInventoryItems = 202,
        GetEconomyConfigurationRealMoneyPurchases = 203,
        GetEconomyGearInventoryItems = 204,
        GetPlayerInventoryItems = 205,
        UpgradePlayerClubInventoryItem = 206,
        RefinePlayerClubInventoryItem = 207,
        GetMultipleConfigs = 208,
        GetBucketsInfo = 209,
        GetMultiplePlayerSaves = 210,
        GetEconomyAvatarInventoryItems = 211,
        SetPlayerAvatar = 212,
        ValidateCurrency = 213,
        SetPlayerGadget = 214,
        GetPlayersInfo = 215,
        GetPlayerNames = 216,
        CheckDbVersionAndMigrate = 217,
        SetPlayerName = 218,
        CheckAndInitializeNewPlayer = 219,
        RegisterSession = 220,
        AddActivityToSession = 221,
        AddShotDataToActivity = 222,
        SearchPlayerByName = 223,
        GetAssetBundleVersions = 224,
        GetAssetBundleUrls = 225,
        GetHoleDownloadUrl = 230,
        GetHoleVersion = 231,
        RegisterDevice = 232,
        GetAllEconomyConfig = 297,
        FetchSupabaseConfig = 298,
        GetCourseConfigByRank = 299,

        // match module
        GetHeadToHeadMatchResult = 300,
        FailBallActionResult = 301,
        FinishMatchClubResult = 302,
        ConsumeGearResult = 303,
        GetH2hLeaderboard = 304,
        GetH2hLeaderboardByPlayerIds = 305,
        ConsumeCoinFee = 306,
        ConsumeInventoryResult = 307,

        // test module
        SetPlayerElo = 400,
        SetPlayerCoins = 401,
        SetPlayerGems = 402,
        SetPlayerClub = 403,
        SetPlayerBall = 404,
        SetPlayerGear = 405,


        // Closes To Pin module
        GetNPassStatus = 600,
        CtpPlayLucky = 601,
        UseNpass= 602,
        GetClosesToPinConfig = 603,
        ClosestToPinReward = 604,
        GetClosesToPinConditionConfig = 605,
        
        //Tournament module
        ValidateParticipation = 700,
        SaveProgress = 701,
        RequestProcessParticipation = 702,
        GetLeaderboard = 703,
        GetReward = 704,
        GetArchivedLeaderboard = 705,
        
        //Dev Module
        CreateChallenge = 800,
        GetAllChallenges = 801,
        UpdateChallenge = 802,
        CreateChallengeTask = 803,
        GetAllChallengeTask = 804,
        UpdateChallengeTask = 805,
        
        //Challenge Module
        RegisterPlayerChallenge = 900,
        GetAllPlayerChallenge = 901,
        ClaimChallengeTaskReward = 902,
        ClaimChallengeMilestoneReward = 903,
        ChallengeTrackPlayModeGame = 910,
        ChallengeTrackWinGameMode = 911,
        ChallengeTrackOpenBag = 912,
        ChallengeTrackSkipBag = 913,
        ChallengeTrackUpgradeClub = 914,
        ChallengeTrackConsumeBall = 915,
        ChallengeTrackReceiveClub = 916,
        ChallengeTrackHoleResult = 917,
        ChallengeTrackPerformShot = 918,
        ChallengeTrackPerformDistanceToPin = 919,
        ChallengeTrackHoleResultTieBreakWithGameMode = 920,
        
        //Message
        GetMessages = 1000,
        CreateOrUpdateMessages = 1001,
        DeleteMessages = 1002,
        DeliverMessageToPlayers = 1003,
        GetActivePlayerMessages = 1004,
        MarkReadPlayerMessages = 1005,
        DeletePlayerMessages = 1006,
        ClaimPlayerMessages = 1007,
        SendP2pMessage = 1008,
        SendP2pGift = 1009,

        //RaceChallengeGame Module
        GetRPassStatus = 1100,
        UseRPass = 1101,
        
        // Tour Module
        GetAllTour = 1200,
        
        //Relationship Module
        GetRelationships = 1300,
    }

    public enum APIResponseCode
    {
        Success = 0,
        Fail_SDK = 1,
        Fail_CodeIneligible = 2,
        Fail = 3,
        Fail_SessionInvalid = 4,
        Fail_SessionExpired = 5
    }
}
