using GolfFantaCore.CommonEntities;
using GolfFantaCore.DTOs;
using GolfFantaCore.DTOs.Tour;
using GolfFantaCore.Models;
using GolfFantaCore.Models.Challenge;
using GolfFantaCore.Models.Challenge.Conditions;
using GolfFantaCore.Models.HeadToHead;
using GolfFantaCore.Models.Message;
using GolfFantaCore.Models.Tour;

namespace GolfFantaCore.Common.Extensions;

public static class TypeConversionExtensions
{
    public static HeadToHeadTour ToHeadToHeadTour(this H2hTourConfig h2HTourConfig)
    {
        return new HeadToHeadTour
        {
            Id = h2HTourConfig.Id,
            CourseId = h2HTourConfig.CourseId,
            DescriptionKey = h2HTourConfig.DescriptionKey,
            EntryFee = h2HTourConfig.EntryFee,
            NameKey = h2HTourConfig.NameKey,
            RequiredElo = h2HTourConfig.RequiredElo,
            WindSpeedMin = h2HTourConfig.WindSpeedMin,
            WindSpeedMax = h2HTourConfig.WindSpeedMax
        };
    }

    public static ChallengeRewardResponse ToChallengeRewardResponse(this EconomyItem model)
    {
        return new ChallengeRewardResponse()
        {
            ItemId = model.ItemId,
            Quantity = model.Quantity
        };
    }
    
    public static ChallengeMilestoneResponse ToChallengeMilestoneResponse(this ChallengeMilestone model)
    {
        return new ChallengeMilestoneResponse()
        {
            MilestonePoint = model.MilestonePoint,
            MilestoneUuid = model.MilestoneUuid,
            Claimed = model.Claimed,
            Rewards = model.Reward.Select(item => item.ToChallengeRewardResponse()).ToList(),
        };
    }
    public static ChallengeResponse ToChallengeResponse(this PlayerChallengeModel model)
    {
        var response = new ChallengeResponse()
        {
            CurrentProgressPoint = model.CalculateProgress(),
            ChallengeType = model.Challenge.ChallengeType.ToString(),
            Id = model.PlayerChallengeId,
            Uuid = model.PlayerChallengeUuid!,
            NameKey = model.Challenge.NameKey!,
            DescriptionKey = model.Challenge.DescriptionKey!,
            Milestones = model.Milestones.Select(item => item.ToChallengeMilestoneResponse()).ToList(),
            ChallengeTasks = model.Tasks.Select(c => c.ToTaskResponse()).ToList(),
            Conditions = model.Challenge.UnlockConditions.Select(c => c.ToConditionResponse()).ToList(),
            StartTime = model.PlayerChallengeCreatedAt,
            ActiveTime = model.GetPlayerChallengeActiveTime,
            EndTime = model.GetPlayerChallengeEndTime
        };
        return response;
    }
    
    public static ChallengeTaskResponse ToTaskResponse(this ChallengeTaskModel model)
    {
        var response = new ChallengeTaskResponse
        {
            Type = model.Type,
            Id = model.PlayerChallengeTaskId,
            Uuid = model.PlayerChallengeTaskUuid,
            NameKey = model.NameKey,
            DescriptionKey = model.DescriptionKey,
            RequiredProgress = model.RequiredProgress,
            UnlockedOnSecond = model.UnlockedOnSecond,
            Progress = model.Progress,
            ProgressReward = model.ProgressReward,
            Parameters = model.Parameters.ToDictionary(pair => pair.Key, pair => pair.Value.ToString()!),
            OtherReward = model.OtherReward.Select(item => item.ToChallengeRewardResponse()).ToList(),
            RewardClaimed = model.RewardClaimed
        };
        return response;
    }
    
    public static ChallengeConditionResponse ToConditionResponse(this IChallengeUnlockCondition model)
    {
        return new ChallengeConditionResponse
        {
            ConditionType = model.Type.ToString(),
            Parameters = model.ToObject(),
            ConditionDescriptionKey = model.ConditionDescriptionKey,
            ConditionNameKey = model.ConditionNameKey
        };
    }

    public static MessageDto ToMessageDto(this MessageModel model)
    {
        return new MessageDto
        {
            Uuid = model.Uuid,
            Title = model.Title,
            Content = model.Content,
            MessageType = model.MessageType,
            DeliveryType = model.DeliveryType,
            Data = JsonUtils.ToJson(model.Data)
        };
    }

    public static PlayerMessageDto ToPlayerMessageDto(this PlayerMessageModel playerMessageModel, MessageModel messageModel)
    {
        return new PlayerMessageDto()
        {
            Uuid = playerMessageModel.Uuid,
            ExpireAt = playerMessageModel.ExpireAt,
            ReadAt = playerMessageModel.ReadAt,
            ClaimedAt = playerMessageModel.ClaimedAt,
            DeletedAt = playerMessageModel.DeletedAt,
            Sender = playerMessageModel.Sender,
            ResponseData = JsonUtils.ToJson(playerMessageModel.ResponseData),
            Title = playerMessageModel.Title,
            Content = playerMessageModel.Content,
            MessageType = playerMessageModel.MessageType,
            DeliveryType = messageModel.DeliveryType,
            Data = JsonUtils.ToJson(playerMessageModel.MessageData)
        };
    }
    
    public static TourDtos ToTourDto(this TourModel model)
    {
        return new TourDtos()
        {
            Uuid = model.Uuid,
            GameMode = model.GameMode,
            Id = model.Id,
            CourseUuid = model.CourseUuid,
            LeaderboardId = model.LeaderboardId,
            Data = model.Data,
            CreatedAt = model.CreatedAt,
            Difficulties = model.Difficulties,
            EntryFees = model.EntryFees,
            Conditions = model.Conditions,
            NameKey = model.NameKey,
            DescriptionKey = model.DescriptionKey,
            BannerAddress = model.BannerAddress,
        };
    }
    

    public static CustomAPIClientRequest ToCustomAPIClientRequest(this APIClientRequest request, string userId)
    {
        return new CustomAPIClientRequest()
        {
            Code = request.code,
            Data = request.data,
            Time = request.time,
            SessionId = request.sessionId,
            RequestId = request.requestId,
            UserId = userId
        };
    }
    
    public static TourCondition ToTourConditionModel(this ConditionEntity tourConditionEntity)
    {
        return new TourCondition()
        {
            ConditionNameKey = tourConditionEntity.ConditionNameKey,
            ConditionDescriptionKey = tourConditionEntity.ConditionDescriptionKey,
            ConditionType = tourConditionEntity.ConditionType,
            Parameters = tourConditionEntity.Parameters.ToDictionary(p => p.Key, p => p.Value),
        };
    }
    
    public static TourEntryFee ToTourEntryFeeModel(this TourEntryFeeEntity tourEntryFeeEntity)
    {
        return new TourEntryFee()
        {
            RankId = tourEntryFeeEntity.rankId,
            Costs = tourEntryFeeEntity.costs.Select(c => new TourEntryCost(c.ItemId, c.Quantity)).ToList()
        };
    }
    
    public static TourDifficult ToTourDifficultModel(this TourDifficultEntity tourDifficultEntity)
    {
        return new TourDifficult()
        {
            RankId = tourDifficultEntity.rankId,
            WindSpeedMax = tourDifficultEntity.windSpeedMax,
            WindSpeedMin = tourDifficultEntity.windSpeedMin,
        };
    }
}