namespace GolfFantaCore.Common.Enums;

public enum EntityStatus
{
    Inactive = 0,
    Active = 1
}


public enum CourseType
{
    Origin,
    Custom
}

public enum ChallengeType
{
    SevenDaysChallenge,
}

public enum DurationMode
{
    Infinity,
    Global,
    Individual
}

public enum ChallengeTaskType
{
    Login,
    Play,
    WinMode,
    UpgradeClub,
    UpgradeClubWithRarity,
    BuyByType,
    BuyItemByTypeWithRarity,
    BuySpecific,
    GetItemByType,
    GetItemByTypeWithRarity,
    GetItemSpecific,
    ConsumeByType,
    ConsumeByTypeWithRarity,
    ConsumeSpecific,
    OpenBag,
    PerformShotWithDistance,
    PerformShotWithRating,
    SkipBag,
    HoleResult,
    PerformDistanceToPin,
    HoleResultTieBreakWithGameMode
}

public enum InventoryRarity
{
    Common = 1,
    Rare = 2,
    Epic = 3,
    Legend = 4
}

public enum MessageType
{
    PlainText,
    Attached,
    Deal
}

public enum DeliveryType
{
    PushNotification
}

public enum ShotRating
{
    None = 0,
    Great = 1,
    Perfect = 2,
}

public enum ParamType
{
    Text,
    SingleOption,
    MultipleOption
}

public enum ParamTarget
{
    Challenge,
    ChallengeTask,
    Tour,
    Notification
}

public enum GolfShotRanking
{
    //HoleInOne = -99,

    Ostrich = -5, 
    Condor = -4,
    Albatross = -3,
    Eagle = -2, 
    Birdie = -1,
    Par = 0,
    Bogey = 1, 
    DoubleBogey = 2,
    TripleBogey = 3, 
    QuadrupleBogey = 4,
    QuintupleBogey = 5,

    //MaximumBogey = 99
}

public enum RelationshipState
{
    None,
    Friend,
    SentRequest,
    ReceivedRequest,
    Block
}

public enum UserAvailability
{
    Unknown,
    Online,
    InMatch,
    Offline
}